#!/bin/bash

# Script to setup <PERSON> pipeline
# This script copies the Jenkinsfile to the project root where <PERSON> expects it

set -e

echo "Setting up Jenkins pipeline..."

# Copy Jenkins<PERSON>le to project root
cp <PERSON><PERSON>le ../Jenkinsfile

echo "Jenkins<PERSON>le copied to project root"
echo ""
echo "Next steps:"
echo "1. Commit and push your changes to Git"
echo "2. <PERSON> <PERSON>, create a new Pipeline job"
echo "3. Configure the job to use 'Pipeline script from SCM'"
echo "4. Set SCM to Git and provide your repository URL"
echo "5. Set the Script Path to 'Jenkinsfile'"
echo "6. Save and run the pipeline"
echo ""
echo "The pipeline will:"
echo "- Build a Docker image with Python 3.10 and Chrome"
echo "- Install dependencies from requirements.txt"
echo "- Run your Robot Framework tests using run_tests.py"
echo "- Publish test results and HTML reports"
