version: '3.8'

services:
  robot-tests:
    build:
      context: ..
      dockerfile: deploy/Dockerfile
    container_name: robot-framework-tests
    volumes:
      # Mount results directory to host for accessing test reports
      - ../results:/app/results
      # Optional: Mount source code for development
      # - ../:/app
    environment:
      - DISPLAY=:99
      - PYTHONUNBUFFERED=1
    # Optional: Add network configuration if tests need to access external services
    # networks:
    #   - test-network
    
    # Optional: Add health check
    healthcheck:
      test: ["CMD", "python", "--version"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s

# Optional: Define custom network
# networks:
#   test-network:
#     driver: bridge
