# Robot Framework Jenkins Deployment

Simple Jenkins pipeline configuration for running Robot Framework tests.

## Files Overview

- **Jenkinsfile**: Jenkins pipeline with virtual environment (requires python3-venv)
- **Jenkinsfile-simple**: Jenkins pipeline without virtual environment (no special requirements)
- **switch-pipeline.sh**: Script to switch between pipeline versions
- **README.md**: This documentation file

## Prerequisites

- Jenkins with Python 3.x and pip3 installed
- HTML Publisher plugin (optional, for better reports)
- For virtual environment version: python3-venv package or sudo access

## Quick Start

If you encounter python3-venv errors, use the simple version:

```bash
cd deploy
./switch-pipeline.sh simple
```

Then commit and push the changes.

## Jenkins Setup

1. **Create a new Pipeline job in Jenkins**
2. **Configure Pipeline script from SCM**
3. **Set Git repository URL**
4. **Set Script Path to `Jenkinsfile`**

## Pipeline Features

- **Simple Python virtual environment setup**
- **Automatic dependency installation**
- **Test execution using run_tests.py**
- **HTML report publishing**
- **Test result archiving**

## Pipeline Stages

1. **Setup & Run Tests**: Creates venv, installs dependencies, runs tests
2. **Publish Results**: Archives results and publishes HTML reports

## Test Results

Test results are saved in the `results` directory and include:

- `output.xml`: Robot Framework XML output (if generated)
- `report.html`: HTML test report (if generated)
- `log.html`: Detailed test log (if generated)
- Screenshots (if any test failures occur)

## Troubleshooting

### Common Issues

1. **python3-venv not available**
   ```
   The virtual environment was not created successfully because ensurepip is not available
   ```
   **Solution**: Use simple version
   ```bash
   cd deploy
   ./switch-pipeline.sh simple
   ```

2. **Python not found**
   - Ensure Python 3.x is installed on Jenkins agent
   - Check PATH configuration

3. **Permission issues**
   - Ensure Jenkins user has write permissions to workspace
   - Simple version uses `--user` flag to avoid permission issues

4. **Dependencies installation fails**
   - Check internet connectivity from Jenkins agent
   - Verify requirements.txt is accessible

5. **Tests fail to run**
   - Check if run_tests.py is executable
   - Verify all test dependencies are installed

### Adding Dependencies

1. Update `requirements.txt` in the project root
2. Pipeline will automatically install new dependencies

### Jenkins Notifications

To add email notifications, modify the `post` section in Jenkinsfile:

```groovy
post {
    always {
        emailext (
            subject: "Test Results: ${currentBuild.fullDisplayName}",
            body: "Test execution completed. Check Jenkins for details.",
            to: "<EMAIL>"
        )
    }
}
```
