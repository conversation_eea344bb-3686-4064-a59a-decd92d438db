# Robot Framework Deployment

This directory contains all the necessary files for deploying and running Robot Framework tests in containerized environments.

## Files Overview

- **Dockerfile**: Docker image configuration with Python 3.10, Chrome, and all dependencies
- **Jenkinsfile**: Jenkins pipeline configuration for CI/CD
- **docker-compose.yml**: Docker Compose configuration for local development
- **build-and-run.sh**: <PERSON>ript to build and run tests locally
- **README.md**: This documentation file

## Prerequisites

- Docker installed and running
- Docker Compose (optional, for docker-compose usage)
- Jenkins with Docker plugin (for CI/CD)

## Local Development

### Option 1: Using the build script (Recommended)

```bash
# Navigate to deploy directory
cd deploy

# Build and run tests
./build-and-run.sh

# Clean build (removes existing containers/images first)
./build-and-run.sh clean
```

### Option 2: Using Docker Compose

```bash
# Navigate to deploy directory
cd deploy

# Build and run
docker-compose up --build

# Run in background
docker-compose up -d --build

# View logs
docker-compose logs -f

# Stop and clean up
docker-compose down
```

### Option 3: Manual Docker commands

```bash
# Build image
docker build -t robot-framework-tests -f deploy/Dockerfile .

# Run tests
docker run --rm -v $(pwd)/results:/app/results robot-framework-tests
```

## Jenkins CI/CD Setup

### 1. Jenkins Configuration

1. Install required Jenkins plugins:
   - Docker Pipeline Plugin
   - HTML Publisher Plugin
   - Robot Framework Plugin (optional)

2. Create a new Pipeline job in Jenkins

3. Configure the pipeline to use the `deploy/Jenkinsfile`

### 2. Pipeline Features

- **Automatic Docker image building**
- **Test execution in isolated containers**
- **Test result archiving**
- **HTML report publishing**
- **Automatic cleanup of old Docker images**
- **Email notifications (can be configured)**

### 3. Pipeline Stages

1. **Checkout**: Gets source code from repository
2. **Build Docker Image**: Creates containerized test environment
3. **Run Tests**: Executes Robot Framework tests
4. **Publish Results**: Archives results and publishes HTML reports

## Environment Variables

The following environment variables can be configured:

- `DOCKER_IMAGE`: Docker image name (default: robot-framework-tests)
- `DOCKER_TAG`: Docker image tag (default: build number)
- `RESULTS_DIR`: Test results directory (default: results)

## Test Results

Test results are saved in the `results` directory and include:

- `output.xml`: Robot Framework XML output
- `report.html`: HTML test report
- `log.html`: Detailed test log
- Screenshots (if any test failures occur)

## Troubleshooting

### Common Issues

1. **Chrome/ChromeDriver compatibility**
   - The Dockerfile automatically installs compatible ChromeDriver version
   - If issues persist, check Chrome and ChromeDriver versions

2. **Display issues**
   - Tests run with Xvfb (virtual display)
   - Display is set to :99 automatically

3. **Permission issues**
   - Ensure Docker has proper permissions
   - Check file permissions for scripts

4. **Memory issues**
   - Increase Docker memory allocation if tests fail due to memory
   - Consider running fewer tests in parallel

### Debugging

To debug issues, you can run the container interactively:

```bash
# Run container with bash shell
docker run -it --rm robot-framework-tests /bin/bash

# Check Chrome installation
google-chrome --version

# Check ChromeDriver
chromedriver --version

# Check Python packages
pip list
```

## Customization

### Adding Dependencies

1. Update `requirements.txt` in the project root
2. Rebuild the Docker image

### Modifying Test Execution

1. Edit the `run_tests.py` script
2. Or modify the Docker CMD in Dockerfile

### Jenkins Notifications

To add email notifications, modify the `post` section in Jenkinsfile:

```groovy
post {
    always {
        emailext (
            subject: "Test Results: ${currentBuild.fullDisplayName}",
            body: "Test execution completed. Check Jenkins for details.",
            to: "<EMAIL>"
        )
    }
}
```

## Security Considerations

- Docker containers run with minimal privileges
- No sensitive data is stored in Docker images
- Use Jenkins credentials for sensitive configuration
- Regularly update base images for security patches

## Performance Tips

- Use Docker layer caching for faster builds
- Consider using multi-stage builds for smaller images
- Run tests in parallel when possible
- Clean up old Docker images regularly
