pipeline {
    agent any
    
    environment {
        // Python environment
        PYTHON_VERSION = "3.10"
        
        // Test results directory
        RESULTS_DIR = "results"
        
        // Virtual environment path
        VENV_PATH = "venv"
    }
    
    options {
        // Keep builds for 30 days
        buildDiscarder(logRotator(daysToKeepStr: '30', numToKeepStr: '50'))
        
        // Timeout for the entire pipeline
        timeout(time: 30, unit: 'MINUTES')
        
        // Timestamps in console output
        timestamps()
    }
    
    stages {
        stage('Checkout') {
            steps {
                echo 'Checking out source code...'
                checkout scm
                
                // Display workspace info
                sh '''
                    echo "Workspace: ${WORKSPACE}"
                    echo "Build Number: ${BUILD_NUMBER}"
                    echo "Branch: ${GIT_BRANCH}"
                    ls -la
                '''
            }
        }
        
        stage('Setup Environment') {
            steps {
                echo 'Setting up Python environment...'
                sh '''
                    # Check Python version
                    python3 --version || python --version
                    
                    # Create virtual environment
                    python3 -m venv ${VENV_PATH} || python -m venv ${VENV_PATH}
                    
                    # Activate virtual environment and install dependencies
                    . ${VENV_PATH}/bin/activate
                    
                    # Upgrade pip
                    pip install --upgrade pip
                    
                    # Install requirements
                    pip install -r requirements.txt
                    
                    # Install additional packages for headless browser testing
                    pip install selenium webdriver-manager
                    
                    # Show installed packages
                    pip list
                '''
            }
        }
        
        stage('Install Chrome') {
            steps {
                echo 'Installing Chrome and ChromeDriver...'
                sh '''
                    # Check if Chrome is already installed
                    if command -v google-chrome >/dev/null 2>&1; then
                        echo "Chrome is already installed"
                        google-chrome --version
                    else
                        echo "Installing Chrome..."
                        
                        # Download and install Chrome (Ubuntu/Debian)
                        wget -q -O - https://dl.google.com/linux/linux_signing_key.pub | sudo apt-key add - || true
                        echo "deb [arch=amd64] http://dl.google.com/linux/chrome/deb/ stable main" | sudo tee /etc/apt/sources.list.d/google-chrome.list || true
                        sudo apt-get update || true
                        sudo apt-get install -y google-chrome-stable || true
                        
                        # Verify installation
                        google-chrome --version || echo "Chrome installation may have failed"
                    fi
                    
                    # Install Xvfb for headless display
                    sudo apt-get install -y xvfb || echo "Xvfb installation may have failed"
                '''
            }
        }
        
        stage('Run Tests') {
            steps {
                echo 'Running Robot Framework tests...'
                sh '''
                    # Create results directory
                    mkdir -p ${RESULTS_DIR}
                    
                    # Activate virtual environment
                    . ${VENV_PATH}/bin/activate
                    
                    # Start Xvfb for headless browser testing
                    export DISPLAY=:99
                    Xvfb :99 -screen 0 1920x1080x24 &
                    XVFB_PID=$!
                    
                    # Wait for Xvfb to start
                    sleep 3
                    
                    # Run tests
                    python run_tests.py || echo "Tests completed with some failures"
                    
                    # Kill Xvfb
                    kill $XVFB_PID || true
                '''
            }
        }
        
        stage('Publish Results') {
            steps {
                echo 'Publishing test results...'
                
                // Archive test results
                archiveArtifacts artifacts: 'results/**/*', allowEmptyArchive: true
                
                // Publish Robot Framework results if available
                script {
                    if (fileExists('results/output.xml')) {
                        publishHTML([
                            allowMissing: false,
                            alwaysLinkToLastBuild: true,
                            keepAll: true,
                            reportDir: 'results',
                            reportFiles: 'report.html',
                            reportName: 'Robot Framework Report',
                            reportTitles: 'Test Report'
                        ])
                        
                        echo 'Robot Framework results published'
                    } else {
                        echo 'No Robot Framework output.xml found'
                    }
                }
            }
        }
    }
    
    post {
        always {
            echo 'Pipeline completed'
            
            // Clean up virtual environment
            sh '''
                if [ -d "${VENV_PATH}" ]; then
                    rm -rf ${VENV_PATH}
                    echo "Virtual environment cleaned up"
                fi
            '''
        }
        
        success {
            echo 'Pipeline succeeded!'
        }
        
        failure {
            echo 'Pipeline failed!'
        }
        
        unstable {
            echo 'Pipeline unstable - some tests failed'
        }
    }
}
