pipeline {
    agent any
    
    environment {
        // Docker image name
        DOCKER_IMAGE = "robot-framework-tests"
        DOCKER_TAG = "${BUILD_NUMBER}"
        
        // Python environment
        PYTHON_VERSION = "3.10"
        
        // Test results directory
        RESULTS_DIR = "results"
    }
    
    options {
        // Keep builds for 30 days
        buildDiscarder(logRotator(daysToKeepStr: '30', numToKeepStr: '50'))
        
        // Timeout for the entire pipeline
        timeout(time: 30, unit: 'MINUTES')
        
        // Timestamps in console output
        timestamps()
    }
    
    stages {
        stage('Checkout') {
            steps {
                echo 'Checking out source code...'
                checkout scm
                
                // Display workspace info
                sh '''
                    echo "Workspace: ${WORKSPACE}"
                    echo "Build Number: ${BUILD_NUMBER}"
                    echo "Branch: ${GIT_BRANCH}"
                    ls -la
                '''
            }
        }
        
        stage('Build Docker Image') {
            steps {
                echo 'Building Docker image...'
                sh '''
                    # Check if Docker is available
                    if ! command -v docker &> /dev/null; then
                        echo "Docker is not installed or not in PATH"
                        exit 1
                    fi

                    # Build Docker image
                    docker build -t ${DOCKER_IMAGE}:${DOCKER_TAG} -f deploy/Dockerfile .

                    # Tag as latest
                    docker tag ${DOCKER_IMAGE}:${DOCKER_TAG} ${DOCKER_IMAGE}:latest

                    echo "Docker image built successfully"
                '''
            }
        }
        
        stage('Run Tests') {
            steps {
                echo 'Running Robot Framework tests...'
                script {
                    try {
                        // Run tests in Docker container
                        sh '''
                            # Create results directory on host
                            mkdir -p ${WORKSPACE}/${RESULTS_DIR}
                            
                            # Run Docker container with volume mount for results
                            docker run --rm \
                                -v ${WORKSPACE}/${RESULTS_DIR}:/app/results \
                                --name robot-tests-${BUILD_NUMBER} \
                                ${DOCKER_IMAGE}:${DOCKER_TAG}
                        '''
                    } catch (Exception e) {
                        echo "Tests completed with some failures: ${e.getMessage()}"
                        // Continue to publish results even if tests failed
                        currentBuild.result = 'UNSTABLE'
                    }
                }
            }
        }
        
        stage('Publish Results') {
            steps {
                echo 'Publishing test results...'
                
                // Archive test results
                archiveArtifacts artifacts: 'results/**/*', allowEmptyArchive: true
                
                // Publish Robot Framework results if available
                script {
                    if (fileExists('results/output.xml')) {
                        publishHTML([
                            allowMissing: false,
                            alwaysLinkToLastBuild: true,
                            keepAll: true,
                            reportDir: 'results',
                            reportFiles: 'report.html',
                            reportName: 'Robot Framework Report',
                            reportTitles: 'Test Report'
                        ])
                        
                        // Parse Robot Framework results
                        step([
                            $class: 'RobotPublisher',
                            outputPath: 'results',
                            outputFileName: 'output.xml',
                            reportFileName: 'report.html',
                            logFileName: 'log.html',
                            disableArchiveOutput: false,
                            passThreshold: 80,
                            unstableThreshold: 60,
                            onlyCritical: true,
                            otherFiles: "**/*.png,**/*.jpg"
                        ])
                    } else {
                        echo 'No Robot Framework output.xml found'
                    }
                }
            }
        }
    }
    
    post {
        always {
            echo 'Pipeline completed'

            // Clean up Docker images (only if Docker is available and accessible)
            script {
                try {
                    sh '''
                        # Check if Docker is accessible
                        if docker info >/dev/null 2>&1; then
                            echo "Cleaning up Docker resources..."

                            # Remove test containers if any are still running
                            docker ps -a | grep robot-tests-${BUILD_NUMBER} | awk '{print $1}' | xargs -r docker rm -f || true

                            # Clean up old images (keep last 5 builds)
                            docker images ${DOCKER_IMAGE} --format "table {{.Tag}}" | tail -n +2 | sort -nr | tail -n +6 | xargs -r -I {} docker rmi ${DOCKER_IMAGE}:{} || true

                            echo "Docker cleanup completed"
                        else
                            echo "Docker not accessible, skipping cleanup"
                        fi
                    '''
                } catch (Exception e) {
                    echo "Docker cleanup failed: ${e.getMessage()}"
                }
            }
        }
        
        success {
            echo 'Pipeline succeeded!'
            // Add notification here if needed
        }
        
        failure {
            echo 'Pipeline failed!'
            // Add notification here if needed
        }
        
        unstable {
            echo 'Pipeline unstable - some tests failed'
            // Add notification here if needed
        }
    }
}
