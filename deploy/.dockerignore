# Git files
.git
.gitignore
.gitattributes

# Documentation
*.md
README*
CHANGELOG*

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS files
.DS_Store
Thumbs.db

# Python cache
__pycache__/
*.pyc
*.pyo
*.pyd
.Python
*.so
.pytest_cache/

# Virtual environments
venv/
env/
ENV/

# Test results (will be generated in container)
results/
*.xml
*.html
*.png
*.jpg

# Logs
*.log

# Temporary files
tmp/
temp/
.tmp/

# Node modules (if any)
node_modules/

# Build artifacts
build/
dist/
*.egg-info/

# Coverage reports
htmlcov/
.coverage
.coverage.*

# Jupyter Notebook
.ipynb_checkpoints

# Docker files (except the one being used)
Dockerfile*
docker-compose*.yml
.dockerignore

# CI/CD files
.github/
.gitlab-ci.yml
azure-pipelines.yml

# Other deployment files
deploy/
!deploy/Dockerfile
