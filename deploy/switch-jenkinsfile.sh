#!/bin/bash

# Script to switch between different Jenkinsfile versions
# Usage: ./switch-jenkinsfile.sh [docker|no-docker]

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}Jenkins Pipeline Switcher${NC}"
echo "========================="

# Check argument
if [ $# -eq 0 ]; then
    echo -e "${YELLOW}Usage: $0 [docker|no-docker]${NC}"
    echo ""
    echo "Available options:"
    echo "  docker    - Use Docker-based pipeline (requires Docker in <PERSON>)"
    echo "  no-docker - Use native Python pipeline (no Docker required)"
    echo ""
    exit 1
fi

case "$1" in
    "docker")
        echo -e "${YELLOW}Switching to Docker-based Jenkinsfile...${NC}"
        cp Jenkinsfile ../Jenkinsfile
        echo -e "${GREEN}✓ Docker Jenkinsfile activated${NC}"
        echo ""
        echo "Requirements:"
        echo "- <PERSON> must have Docker installed"
        echo "- Jenkins user must have Docker permissions"
        echo "- Docker Pipeline plugin recommended"
        ;;
    "no-docker")
        echo -e "${YELLOW}Switching to non-Docker Jenkinsfile...${NC}"
        cp Jenkinsfile-no-docker ../Jenkinsfile
        echo -e "${GREEN}✓ Non-Docker Jenkinsfile activated${NC}"
        echo ""
        echo "Requirements:"
        echo "- Jenkins must have Python 3.x installed"
        echo "- Jenkins must have sudo access for Chrome installation"
        echo "- HTML Publisher plugin recommended"
        ;;
    *)
        echo -e "${RED}Error: Invalid option '$1'${NC}"
        echo "Use 'docker' or 'no-docker'"
        exit 1
        ;;
esac

echo ""
echo -e "${BLUE}Next steps:${NC}"
echo "1. Commit and push the updated Jenkinsfile"
echo "2. Run your Jenkins pipeline"
echo ""
echo -e "${YELLOW}Note: The Jenkinsfile has been copied to the project root${NC}"
