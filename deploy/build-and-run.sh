#!/bin/bash

# Script to build and run Robot Framework tests locally
# Usage: ./build-and-run.sh [clean]

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
DOCKER_IMAGE="robot-framework-tests"
DOCKER_TAG="latest"
RESULTS_DIR="../results"

echo -e "${BLUE}Robot Framework Docker Test Runner${NC}"
echo "=================================="

# Check if clean flag is provided
if [ "$1" = "clean" ]; then
    echo -e "${YELLOW}Cleaning up existing containers and images...${NC}"
    
    # Stop and remove existing containers
    docker ps -a | grep $DOCKER_IMAGE | awk '{print $1}' | xargs -r docker rm -f
    
    # Remove existing images
    docker images | grep $DOCKER_IMAGE | awk '{print $3}' | xargs -r docker rmi -f
    
    echo -e "${GREEN}Cleanup completed${NC}"
fi

# Create results directory if it doesn't exist
echo -e "${YELLOW}Creating results directory...${NC}"
mkdir -p $RESULTS_DIR

# Build Docker image
echo -e "${YELLOW}Building Docker image...${NC}"
cd "$(dirname "$0")"  # Change to script directory
docker build -t $DOCKER_IMAGE:$DOCKER_TAG -f Dockerfile ..

if [ $? -eq 0 ]; then
    echo -e "${GREEN}Docker image built successfully${NC}"
else
    echo -e "${RED}Failed to build Docker image${NC}"
    exit 1
fi

# Run tests
echo -e "${YELLOW}Running Robot Framework tests...${NC}"
docker run --rm \
    -v "$(pwd)/$RESULTS_DIR:/app/results" \
    --name robot-tests-$(date +%s) \
    $DOCKER_IMAGE:$DOCKER_TAG

TEST_EXIT_CODE=$?

# Check results
if [ $TEST_EXIT_CODE -eq 0 ]; then
    echo -e "${GREEN}Tests completed successfully!${NC}"
else
    echo -e "${YELLOW}Tests completed with some failures (exit code: $TEST_EXIT_CODE)${NC}"
fi

# Display results location
echo -e "${BLUE}Test results are available in: $RESULTS_DIR${NC}"

# Check if report.html exists and provide instructions
if [ -f "$RESULTS_DIR/report.html" ]; then
    echo -e "${GREEN}Test report: file://$(pwd)/$RESULTS_DIR/report.html${NC}"
    echo -e "${BLUE}Open the report.html file in your browser to view detailed results${NC}"
fi

exit $TEST_EXIT_CODE
