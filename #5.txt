Started by an SCM change
Obtained <PERSON><PERSON><PERSON> from git https://github.com/rainscales-qc-automation/clone-clo-auto.git
[Pipeline] Start of Pipeline
[Pipeline] node
Running on Jenkins
 in /var/lib/jenkins/workspace/v-job
[Pipeline] {
[Pipeline] stage
[Pipeline] { (Declarative: Checkout SCM)
[Pipeline] checkout
The recommended git tool is: git
using credential 348a7930-21a5-4eb7-bad5-9b230cce1689
 > git rev-parse --resolve-git-dir /var/lib/jenkins/workspace/v-job/.git # timeout=10
Fetching changes from the remote Git repository
 > git config remote.origin.url https://github.com/rainscales-qc-automation/clone-clo-auto.git # timeout=10
Fetching upstream changes from https://github.com/rainscales-qc-automation/clone-clo-auto.git
 > git --version # timeout=10
 > git --version # 'git version 2.25.1'
using GIT_ASKPASS to set credentials 
 > git fetch --tags --force --progress -- https://github.com/rainscales-qc-automation/clone-clo-auto.git +refs/heads/*:refs/remotes/origin/* # timeout=10
 > git rev-parse refs/remotes/origin/main^{commit} # timeout=10
Checking out Revision 151864bfac858a52b64cecda904127ab18038171 (refs/remotes/origin/main)
 > git config core.sparsecheckout # timeout=10
 > git checkout -f 151864bfac858a52b64cecda904127ab18038171 # timeout=10
Commit message: "rrr"
 > git rev-list --no-walk c0a30bfef5785978ca643b3eb84b3a652e10f9b8 # timeout=10
[Pipeline] }
[Pipeline] // stage
[Pipeline] withEnv
[Pipeline] {
[Pipeline] withEnv
[Pipeline] {
[Pipeline] timeout
Timeout set to expire in 30 min
[Pipeline] {
[Pipeline] timestamps
[Pipeline] {
[Pipeline] stage
[Pipeline] { (Setup & Run Tests)
[Pipeline] echo
15:15:10  Setting up environment and running tests...
[Pipeline] sh
15:15:11  + python3 --version
15:15:11  Python 3.8.10
15:15:11  + pip3 install --user --upgrade pip
15:15:13  Collecting pip
15:15:13    Using cached pip-25.0.1-py3-none-any.whl (1.8 MB)
15:15:15  Installing collected packages: pip
15:15:16    WARNING: The scripts pip, pip3 and pip3.8 are installed in '/var/lib/jenkins/.local/bin' which is not on PATH.
15:15:16    Consider adding this directory to PATH or, if you prefer to suppress this warning, use --no-warn-script-location.
15:15:16  Successfully installed pip-25.0.1
15:15:17  + pip3 install --user -r requirements.txt
15:15:18  Requirement already satisfied: robotframework==7.3.1 in /var/lib/jenkins/.local/lib/python3.8/site-packages (from -r requirements.txt (line 1)) (7.3.1)
15:15:18  Requirement already satisfied: robotframework-seleniumlibrary==6.7.1 in /var/lib/jenkins/.local/lib/python3.8/site-packages (from -r requirements.txt (line 2)) (6.7.1)
15:15:18  Requirement already satisfied: gspread==6.2.1 in /var/lib/jenkins/.local/lib/python3.8/site-packages (from -r requirements.txt (line 3)) (6.2.1)
15:15:18  Requirement already satisfied: robotframework-pythonlibcore>=4.4.1 in /var/lib/jenkins/.local/lib/python3.8/site-packages (from robotframework-seleniumlibrary==6.7.1->-r requirements.txt (line 2)) (4.4.1)
15:15:18  Requirement already satisfied: selenium>=4.3.0 in /var/lib/jenkins/.local/lib/python3.8/site-packages (from robotframework-seleniumlibrary==6.7.1->-r requirements.txt (line 2)) (4.27.1)
15:15:18  Requirement already satisfied: click>=8.0 in /var/lib/jenkins/.local/lib/python3.8/site-packages (from robotframework-seleniumlibrary==6.7.1->-r requirements.txt (line 2)) (8.1.8)
15:15:18  Requirement already satisfied: google-auth>=1.12.0 in /var/lib/jenkins/.local/lib/python3.8/site-packages (from gspread==6.2.1->-r requirements.txt (line 3)) (2.40.3)
15:15:19  Requirement already satisfied: google-auth-oauthlib>=0.4.1 in /var/lib/jenkins/.local/lib/python3.8/site-packages (from gspread==6.2.1->-r requirements.txt (line 3)) (1.2.2)
15:15:19  Requirement already satisfied: trio-websocket~=0.9 in /var/lib/jenkins/.local/lib/python3.8/site-packages (from selenium>=4.3.0->robotframework-seleniumlibrary==6.7.1->-r requirements.txt (line 2)) (0.12.2)
15:15:19  Requirement already satisfied: typing_extensions~=4.9 in /var/lib/jenkins/.local/lib/python3.8/site-packages (from selenium>=4.3.0->robotframework-seleniumlibrary==6.7.1->-r requirements.txt (line 2)) (4.13.2)
15:15:19  Requirement already satisfied: trio~=0.17 in /var/lib/jenkins/.local/lib/python3.8/site-packages (from selenium>=4.3.0->robotframework-seleniumlibrary==6.7.1->-r requirements.txt (line 2)) (0.27.0)
15:15:19  Requirement already satisfied: certifi>=2021.10.8 in /var/lib/jenkins/.local/lib/python3.8/site-packages (from selenium>=4.3.0->robotframework-seleniumlibrary==6.7.1->-r requirements.txt (line 2)) (2025.7.14)
15:15:19  Requirement already satisfied: websocket-client~=1.8 in /var/lib/jenkins/.local/lib/python3.8/site-packages (from selenium>=4.3.0->robotframework-seleniumlibrary==6.7.1->-r requirements.txt (line 2)) (1.8.0)
15:15:19  Requirement already satisfied: urllib3[socks]<3,>=1.26 in /var/lib/jenkins/.local/lib/python3.8/site-packages (from selenium>=4.3.0->robotframework-seleniumlibrary==6.7.1->-r requirements.txt (line 2)) (2.2.3)
15:15:19  Requirement already satisfied: rsa<5,>=3.1.4 in /var/lib/jenkins/.local/lib/python3.8/site-packages (from google-auth>=1.12.0->gspread==6.2.1->-r requirements.txt (line 3)) (4.9.1)
15:15:19  Requirement already satisfied: cachetools<6.0,>=2.0.0 in /var/lib/jenkins/.local/lib/python3.8/site-packages (from google-auth>=1.12.0->gspread==6.2.1->-r requirements.txt (line 3)) (5.5.2)
15:15:19  Requirement already satisfied: pyasn1-modules>=0.2.1 in /usr/lib/python3/dist-packages (from google-auth>=1.12.0->gspread==6.2.1->-r requirements.txt (line 3)) (0.2.1)
15:15:19  Requirement already satisfied: requests-oauthlib>=0.7.0 in /var/lib/jenkins/.local/lib/python3.8/site-packages (from google-auth-oauthlib>=0.4.1->gspread==6.2.1->-r requirements.txt (line 3)) (2.0.0)
15:15:19  Requirement already satisfied: outcome>=1.2.0 in /var/lib/jenkins/.local/lib/python3.8/site-packages (from trio-websocket~=0.9->selenium>=4.3.0->robotframework-seleniumlibrary==6.7.1->-r requirements.txt (line 2)) (1.3.0.post0)
15:15:19  Requirement already satisfied: wsproto>=0.14 in /var/lib/jenkins/.local/lib/python3.8/site-packages (from trio-websocket~=0.9->selenium>=4.3.0->robotframework-seleniumlibrary==6.7.1->-r requirements.txt (line 2)) (1.2.0)
15:15:19  Requirement already satisfied: exceptiongroup; python_version < "3.11" in /usr/local/lib/python3.8/dist-packages (from trio-websocket~=0.9->selenium>=4.3.0->robotframework-seleniumlibrary==6.7.1->-r requirements.txt (line 2)) (1.1.3)
15:15:19  Requirement already satisfied: attrs>=23.2.0 in /var/lib/jenkins/.local/lib/python3.8/site-packages (from trio~=0.17->selenium>=4.3.0->robotframework-seleniumlibrary==6.7.1->-r requirements.txt (line 2)) (25.3.0)
15:15:19  Requirement already satisfied: sniffio>=1.3.0 in /usr/local/lib/python3.8/dist-packages (from trio~=0.17->selenium>=4.3.0->robotframework-seleniumlibrary==6.7.1->-r requirements.txt (line 2)) (1.3.0)
15:15:19  Requirement already satisfied: idna in /usr/lib/python3/dist-packages (from trio~=0.17->selenium>=4.3.0->robotframework-seleniumlibrary==6.7.1->-r requirements.txt (line 2)) (2.8)
15:15:19  Requirement already satisfied: sortedcontainers in /var/lib/jenkins/.local/lib/python3.8/site-packages (from trio~=0.17->selenium>=4.3.0->robotframework-seleniumlibrary==6.7.1->-r requirements.txt (line 2)) (2.4.0)
15:15:19  Requirement already satisfied: pysocks!=1.5.7,<2.0,>=1.5.6; extra == "socks" in /var/lib/jenkins/.local/lib/python3.8/site-packages (from urllib3[socks]<3,>=1.26->selenium>=4.3.0->robotframework-seleniumlibrary==6.7.1->-r requirements.txt (line 2)) (1.7.1)
15:15:19  Requirement already satisfied: pyasn1>=0.1.3 in /usr/lib/python3/dist-packages (from rsa<5,>=3.1.4->google-auth>=1.12.0->gspread==6.2.1->-r requirements.txt (line 3)) (0.4.2)
15:15:19  Requirement already satisfied: requests>=2.0.0 in /usr/local/lib/python3.8/dist-packages (from requests-oauthlib>=0.7.0->google-auth-oauthlib>=0.4.1->gspread==6.2.1->-r requirements.txt (line 3)) (2.31.0)
15:15:19  Requirement already satisfied: oauthlib>=3.0.0 in /usr/lib/python3/dist-packages (from requests-oauthlib>=0.7.0->google-auth-oauthlib>=0.4.1->gspread==6.2.1->-r requirements.txt (line 3)) (3.1.0)
15:15:19  Requirement already satisfied: h11<1,>=0.9.0 in /var/lib/jenkins/.local/lib/python3.8/site-packages (from wsproto>=0.14->trio-websocket~=0.9->selenium>=4.3.0->robotframework-seleniumlibrary==6.7.1->-r requirements.txt (line 2)) (0.16.0)
15:15:19  Requirement already satisfied: charset-normalizer<4,>=2 in /usr/local/lib/python3.8/dist-packages (from requests>=2.0.0->requests-oauthlib>=0.7.0->google-auth-oauthlib>=0.4.1->gspread==6.2.1->-r requirements.txt (line 3)) (3.3.1)
15:15:20  + export PATH=/var/lib/jenkins/.local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/snap/bin
15:15:20  + python3 test_robot_command.py
15:15:21  Testing Robot Framework command availability...
15:15:21  ==================================================
15:15:21  ✓ Found robot command at: /var/lib/jenkins/.local/bin/robot
15:15:21  ✓ Robot version: Robot Framework 7.3.1 (Python 3.8.10 on linux)
15:15:21  
15:15:21  ==================================================
15:15:21  Testing installed packages...
15:15:21  ✓ robotframework imported successfully
15:15:21  ✓ Robot Framework version: 7.3.1
15:15:21  ✓ SeleniumLibrary imported successfully
15:15:21  ✓ gspread imported successfully
15:15:21  
15:15:21  ==================================================
15:15:21  SUMMARY
15:15:21  ==================================================
15:15:21  ✓ Robot command: /var/lib/jenkins/.local/bin/robot 
15:15:21  ✓ Robot Framework is ready to use!
15:15:21  + mkdir -p results
15:15:21  + python3 run_tests.py
15:18:43  Robot Framework Test Runner
15:18:43  ========================================
15:18:43  ============================================================
15:18:43  STARTING ROBOT FRAMEWORK TEST EXECUTION CYCLE
15:18:43  ============================================================
15:18:43  Starting Robot Framework test execution at 2025-07-31 15:15:21.683068
15:18:43  Test directory: /var/lib/jenkins/workspace/v-job/test
15:18:43  Results directory: /var/lib/jenkins/workspace/v-job/results
15:18:43  Executing command: /var/lib/jenkins/.local/bin/robot --outputdir /var/lib/jenkins/workspace/v-job/results --output /var/lib/jenkins/workspace/v-job/results/output.xml --log /var/lib/jenkins/workspace/v-job/results/log.html --report /var/lib/jenkins/workspace/v-job/results/report.html --loglevel INFO /var/lib/jenkins/workspace/v-job/test
15:18:43  Robot Framework execution completed with return code: 34
15:18:43  STDOUT:
15:18:43  ==============================================================================
15:18:43  Test                                                                          
15:18:43  ==============================================================================
15:18:43  Test.Add Assessment Test                                                      
15:18:43  ==============================================================================
15:18:43  Verify Form Add Assessment is Displayed :: Test case to verify the... | FAIL |
15:18:43  Setup failed:
15:18:43  SessionNotCreatedException: Message: session not created: probably user data directory is already in use, please specify a unique value for --user-data-dir argument, or don't use --user-data-dir
15:18:43  Stacktrace:
15:18:43  #0 0x56205219f2ca <unknown>
15:18:43  #1 0x562051c46550 <unknown>
15:18:43  #2 0x562051c809cb <unknown>
15:18:43  #3 0x562051c7bd17 <unknown>
15:18:43  #4 0x562051ccc19e <unknown>
15:18:43  #5 0x562051ccb766 <unknown>
15:18:43  #6 0x562051cbd993 <unknown>
15:18:43  #7 0x562051c89d6b <unknown>
15:18:43  #8 0x562051c8b141 <unknown>
15:18:43  #9 0x5620521642ab <unknown>
15:18:43  #10 0x5620521680b9 <unknown>
15:18:43  #11 0x56205214b139 <unknown>
15:18:43  #12 0x562052168c68 <unknown>
15:18:43  #13 0x56205212f60f <unknown>
15:18:43  #14 0x56205218d1f8 <unknown>
15:18:43  #15 0x56205218d3d6 <unknown>
15:18:43  #16 0x56205219e5e6 <unknown>
15:18:43  #17 0x7f27a822e609 start_thread
15:18:43  ------------------------------------------------------------------------------
15:18:43  Verify Add Assessment Successfully :: Test case to add an assessment  | FAIL |
15:18:43  Setup failed:
15:18:43  SessionNotCreatedException: Message: session not created: probably user data directory is already in use, please specify a unique value for --user-data-dir argument, or don't use --user-data-dir
15:18:43  Stacktrace:
15:18:43  #0 0x556a68d052ca <unknown>
15:18:43  #1 0x556a687ac550 <unknown>
15:18:43  #2 0x556a687e69cb <unknown>
15:18:43  #3 0x556a687e1d17 <unknown>
15:18:43  #4 0x556a6883219e <unknown>
15:18:43  #5 0x556a68831766 <unknown>
15:18:43  #6 0x556a68823993 <unknown>
15:18:43  #7 0x556a687efd6b <unknown>
15:18:43  #8 0x556a687f1141 <unknown>
15:18:43  #9 0x556a68cca2ab <unknown>
15:18:43  #10 0x556a68cce0b9 <unknown>
15:18:43  #11 0x556a68cb1139 <unknown>
15:18:43  #12 0x556a68ccec68 <unknown>
15:18:43  #13 0x556a68c9560f <unknown>
15:18:43  #14 0x556a68cf31f8 <unknown>
15:18:43  #15 0x556a68cf33d6 <unknown>
15:18:43  #16 0x556a68d045e6 <unknown>
15:18:43  #17 0x7f9cce9d2609 start_thread
15:18:43  ------------------------------------------------------------------------------
15:18:43  Verify Leave Required Fields Empty :: Test case Bỏ trống các trườn... | FAIL |
15:18:43  Setup failed:
15:18:43  SessionNotCreatedException: Message: session not created: probably user data directory is already in use, please specify a unique value for --user-data-dir argument, or don't use --user-data-dir
15:18:43  Stacktrace:
15:18:43  #0 0x5580393242ca <unknown>
15:18:43  #1 0x558038dcb550 <unknown>
15:18:43  #2 0x558038e059cb <unknown>
15:18:43  #3 0x558038e00d17 <unknown>
15:18:43  #4 0x558038e5119e <unknown>
15:18:43  #5 0x558038e50766 <unknown>
15:18:43  #6 0x558038e42993 <unknown>
15:18:43  #7 0x558038e0ed6b <unknown>
15:18:43  #8 0x558038e10141 <unknown>
15:18:43  #9 0x5580392e92ab <unknown>
15:18:43  #10 0x5580392ed0b9 <unknown>
15:18:43  #11 0x5580392d0139 <unknown>
15:18:43  #12 0x5580392edc68 <unknown>
15:18:43  #13 0x5580392b460f <unknown>
15:18:43  #14 0x5580393121f8 <unknown>
15:18:43  #15 0x5580393123d6 <unknown>
15:18:43  #16 0x5580393235e6 <unknown>
15:18:43  #17 0x7f8bcae1d609 start_thread
15:18:43  ------------------------------------------------------------------------------
15:18:43  Verify Fill Assessment With HighWeight :: Nhập tỷ trọng > 100         | FAIL |
15:18:43  Setup failed:
15:18:43  SessionNotCreatedException: Message: session not created: probably user data directory is already in use, please specify a unique value for --user-data-dir argument, or don't use --user-data-dir
15:18:43  Stacktrace:
15:18:43  #0 0x55af0cfe52ca <unknown>
15:18:43  #1 0x55af0ca8c550 <unknown>
15:18:43  #2 0x55af0cac69cb <unknown>
15:18:43  #3 0x55af0cac1d17 <unknown>
15:18:43  #4 0x55af0cb1219e <unknown>
15:18:43  #5 0x55af0cb11766 <unknown>
15:18:43  #6 0x55af0cb03993 <unknown>
15:18:43  #7 0x55af0cacfd6b <unknown>
15:18:43  #8 0x55af0cad1141 <unknown>
15:18:43  #9 0x55af0cfaa2ab <unknown>
15:18:43  #10 0x55af0cfae0b9 <unknown>
15:18:43  #11 0x55af0cf91139 <unknown>
15:18:43  #12 0x55af0cfaec68 <unknown>
15:18:43  #13 0x55af0cf7560f <unknown>
15:18:43  #14 0x55af0cfd31f8 <unknown>
15:18:43  #15 0x55af0cfd33d6 <unknown>
15:18:43  #16 0x55af0cfe45e6 <unknown>
15:18:43  #17 0x7f5c65453609 start_thread
15:18:43  ------------------------------------------------------------------------------
15:18:43  Verify Fill Assessment WithInvalidPassScore :: Điểm đạt > Tổng điểm   | FAIL |
15:18:43  Setup failed:
15:18:43  SessionNotCreatedException: Message: session not created: probably user data directory is already in use, please specify a unique value for --user-data-dir argument, or don't use --user-data-dir
15:18:43  Stacktrace:
15:18:43  #0 0x56239715d2ca <unknown>
15:18:43  #1 0x562396c04550 <unknown>
15:18:43  #2 0x562396c3e9cb <unknown>
15:18:43  #3 0x562396c39d17 <unknown>
15:18:43  #4 0x562396c8a19e <unknown>
15:18:43  #5 0x562396c89766 <unknown>
15:18:43  #6 0x562396c7b993 <unknown>
15:18:43  #7 0x562396c47d6b <unknown>
15:18:43  #8 0x562396c49141 <unknown>
15:18:43  #9 0x5623971222ab <unknown>
15:18:43  #10 0x5623971260b9 <unknown>
15:18:43  #11 0x562397109139 <unknown>
15:18:43  #12 0x562397126c68 <unknown>
15:18:43  #13 0x5623970ed60f <unknown>
15:18:43  #14 0x56239714b1f8 <unknown>
15:18:43  #15 0x56239714b3d6 <unknown>
15:18:43  #16 0x56239715c5e6 <unknown>
15:18:43  #17 0x7fb6d0ab3609 start_thread
15:18:43  ------------------------------------------------------------------------------
15:18:43  Verify Fill Assessment WithSpecialDescription :: Tên đánh giá có k... | FAIL |
15:18:43  Setup failed:
15:18:43  SessionNotCreatedException: Message: session not created: probably user data directory is already in use, please specify a unique value for --user-data-dir argument, or don't use --user-data-dir
15:18:43  Stacktrace:
15:18:43  #0 0x5584e4fb92ca <unknown>
15:18:43  #1 0x5584e4a60550 <unknown>
15:18:43  #2 0x5584e4a9a9cb <unknown>
15:18:43  #3 0x5584e4a95d17 <unknown>
15:18:43  #4 0x5584e4ae619e <unknown>
15:18:43  #5 0x5584e4ae5766 <unknown>
15:18:43  #6 0x5584e4ad7993 <unknown>
15:18:43  #7 0x5584e4aa3d6b <unknown>
15:18:43  #8 0x5584e4aa5141 <unknown>
15:18:43  #9 0x5584e4f7e2ab <unknown>
15:18:43  #10 0x5584e4f820b9 <unknown>
15:18:43  #11 0x5584e4f65139 <unknown>
15:18:43  #12 0x5584e4f82c68 <unknown>
15:18:43  #13 0x5584e4f4960f <unknown>
15:18:43  #14 0x5584e4fa71f8 <unknown>
15:18:43  #15 0x5584e4fa73d6 <unknown>
15:18:43  #16 0x5584e4fb85e6 <unknown>
15:18:43  #17 0x7fe5d6d09609 start_thread
15:18:43  ------------------------------------------------------------------------------
15:18:43  Test.Add Assessment Test                                              | FAIL |
15:18:43  6 tests, 0 passed, 6 failed
15:18:43  ==============================================================================
15:18:43  Test.Browser Questions Test                                                   
15:18:43  ==============================================================================
15:18:43  TC_BrowserQs_001: Approve Question :: User Admin Approve Question     | FAIL |
15:18:43  Setup failed:
15:18:43  SessionNotCreatedException: Message: session not created: probably user data directory is already in use, please specify a unique value for --user-data-dir argument, or don't use --user-data-dir
15:18:43  Stacktrace:
15:18:43  #0 0x556a803e72ca <unknown>
15:18:43  #1 0x556a7fe8e550 <unknown>
15:18:43  #2 0x556a7fec89cb <unknown>
15:18:43  #3 0x556a7fec3d17 <unknown>
15:18:43  #4 0x556a7ff1419e <unknown>
15:18:43  #5 0x556a7ff13766 <unknown>
15:18:43  #6 0x556a7ff05993 <unknown>
15:18:43  #7 0x556a7fed1d6b <unknown>
15:18:43  #8 0x556a7fed3141 <unknown>
15:18:43  #9 0x556a803ac2ab <unknown>
15:18:43  #10 0x556a803b00b9 <unknown>
15:18:43  #11 0x556a80393139 <unknown>
15:18:43  #12 0x556a803b0c68 <unknown>
15:18:43  #13 0x556a8037760f <unknown>
15:18:43  #14 0x556a803d51f8 <unknown>
15:18:43  #15 0x556a803d53d6 <unknown>
15:18:43  #16 0x556a803e65e6 <unknown>
15:18:43  #17 0x7f7bf450d609 start_thread
15:18:43  ------------------------------------------------------------------------------
15:18:43  TC_BrowserQs_002: Reject Question :: User Admin Reject Question       | FAIL |
15:18:43  Setup failed:
15:18:43  SessionNotCreatedException: Message: session not created: probably user data directory is already in use, please specify a unique value for --user-data-dir argument, or don't use --user-data-dir
15:18:43  Stacktrace:
15:18:43  #0 0x5633582f32ca <unknown>
15:18:43  #1 0x563357d9a550 <unknown>
15:18:43  #2 0x563357dd49cb <unknown>
15:18:43  #3 0x563357dcfd17 <unknown>
15:18:43  #4 0x563357e2019e <unknown>
15:18:43  #5 0x563357e1f766 <unknown>
15:18:43  #6 0x563357e11993 <unknown>
15:18:43  #7 0x563357dddd6b <unknown>
15:18:43  #8 0x563357ddf141 <unknown>
15:18:43  #9 0x5633582b82ab <unknown>
15:18:43  #10 0x5633582bc0b9 <unknown>
15:18:43  #11 0x56335829f139 <unknown>
15:18:43  #12 0x5633582bcc68 <unknown>
15:18:43  #13 0x56335828360f <unknown>
15:18:43  #14 0x5633582e11f8 <unknown>
15:18:43  #15 0x5633582e13d6 <unknown>
15:18:43  #16 0x5633582f25e6 <unknown>
15:18:43  #17 0x7f81651e9609 start_thread
15:18:43  ------------------------------------------------------------------------------
15:18:43  TC_BrowserQs_003: Filter Course :: Filter Course: CS301: Cơ sở dữ ... | FAIL |
15:18:43  Setup failed:
15:18:43  SessionNotCreatedException: Message: session not created: probably user data directory is already in use, please specify a unique value for --user-data-dir argument, or don't use --user-data-dir
15:18:43  Stacktrace:
15:18:43  #0 0x5645f7afb2ca <unknown>
15:18:43  #1 0x5645f75a2550 <unknown>
15:18:43  #2 0x5645f75dc9cb <unknown>
15:18:43  #3 0x5645f75d7d17 <unknown>
15:18:43  #4 0x5645f762819e <unknown>
15:18:43  #5 0x5645f7627766 <unknown>
15:18:43  #6 0x5645f7619993 <unknown>
15:18:43  #7 0x5645f75e5d6b <unknown>
15:18:43  #8 0x5645f75e7141 <unknown>
15:18:43  #9 0x5645f7ac02ab <unknown>
15:18:43  #10 0x5645f7ac40b9 <unknown>
15:18:43  #11 0x5645f7aa7139 <unknown>
15:18:43  #12 0x5645f7ac4c68 <unknown>
15:18:43  #13 0x5645f7a8b60f <unknown>
15:18:43  #14 0x5645f7ae91f8 <unknown>
15:18:43  #15 0x5645f7ae93d6 <unknown>
15:18:43  #16 0x5645f7afa5e6 <unknown>
15:18:43  #17 0x7f21848dc609 start_thread
15:18:43  ------------------------------------------------------------------------------
15:18:43  Test.Browser Questions Test                                           | FAIL |
15:18:43  3 tests, 0 passed, 3 failed
15:18:43  ==============================================================================
15:18:43  Test.Chapter Test                                                             
15:18:43  ==============================================================================
15:18:43  Tcs 01: Func-Chapter: Create Chapter with fully field :: Kiểm tra ... | FAIL |
15:18:43  Setup failed:
15:18:43  SessionNotCreatedException: Message: session not created: probably user data directory is already in use, please specify a unique value for --user-data-dir argument, or don't use --user-data-dir
15:18:43  Stacktrace:
15:18:43  #0 0x564ff61f72ca <unknown>
15:18:43  #1 0x564ff5c9e550 <unknown>
15:18:43  #2 0x564ff5cd89cb <unknown>
15:18:43  #3 0x564ff5cd3d17 <unknown>
15:18:43  #4 0x564ff5d2419e <unknown>
15:18:43  #5 0x564ff5d23766 <unknown>
15:18:43  #6 0x564ff5d15993 <unknown>
15:18:43  #7 0x564ff5ce1d6b <unknown>
15:18:43  #8 0x564ff5ce3141 <unknown>
15:18:43  #9 0x564ff61bc2ab <unknown>
15:18:43  #10 0x564ff61c00b9 <unknown>
15:18:43  #11 0x564ff61a3139 <unknown>
15:18:43  #12 0x564ff61c0c68 <unknown>
15:18:43  #13 0x564ff618760f <unknown>
15:18:43  #14 0x564ff61e51f8 <unknown>
15:18:43  #15 0x564ff61e53d6 <unknown>
15:18:43  #16 0x564ff61f65e6 <unknown>
15:18:43  #17 0x7f1d7cf7d609 start_thread
15:18:43  ------------------------------------------------------------------------------
15:18:43  Test.Chapter Test                                                     | FAIL |
15:18:43  1 test, 0 passed, 1 failed
15:18:43  ==============================================================================
15:18:43  Test.Course Test                                                              
15:18:43  ==============================================================================
15:18:43  Tcs 01: Func-Course-03: Create Course with fully field :: Kiểm tra... | FAIL |
15:18:43  Parent suite setup failed:
15:18:43  SessionNotCreatedException: Message: session not created: probably user data directory is already in use, please specify a unique value for --user-data-dir argument, or don't use --user-data-dir
15:18:43  Stacktrace:
15:18:43  #0 0x564b7e8c22ca <unknown>
15:18:43  #1 0x564b7e369550 <unknown>
15:18:43  #2 0x564b7e3a39cb <unknown>
15:18:43  #3 0x564b7e39ed17 <unknown>
15:18:43  #4 0x564b7e3ef19e <unknown>
15:18:43  #5 0x564b7e3ee766 <unknown>
15:18:43  #6 0x564b7e3e0993 <unknown>
15:18:43  #7 0x564b7e3acd6b <unknown>
15:18:43  #8 0x564b7e3ae141 <unknown>
15:18:43  #9 0x564b7e8872ab <unknown>
15:18:43  #10 0x564b7e88b0b9 <unknown>
15:18:43  #11 0x564b7e86e139 <unknown>
15:18:43  #12 0x564b7e88bc68 <unknown>
15:18:43  #13 0x564b7e85260f <unknown>
15:18:43  #14 0x564b7e8b01f8 <unknown>
15:18:43  #15 0x564b7e8b03d6 <unknown>
15:18:43  #16 0x564b7e8c15e6 <unknown>
15:18:43  #17 0x7f4d7efcb609 start_thread
15:18:43  ------------------------------------------------------------------------------
15:18:43  Tcs 02: Func-Course-04: Create course with empty required fields :... | FAIL |
15:18:43  Parent suite setup failed:
15:18:43  SessionNotCreatedException: Message: session not created: probably user data directory is already in use, please specify a unique value for --user-data-dir argument, or don't use --user-data-dir
15:18:43  Stacktrace:
15:18:43  #0 0x564b7e8c22ca <unknown>
15:18:43  #1 0x564b7e369550 <unknown>
15:18:43  #2 0x564b7e3a39cb <unknown>
15:18:43  #3 0x564b7e39ed17 <unknown>
15:18:43  #4 0x564b7e3ef19e <unknown>
15:18:43  #5 0x564b7e3ee766 <unknown>
15:18:43  #6 0x564b7e3e0993 <unknown>
15:18:43  #7 0x564b7e3acd6b <unknown>
15:18:43  #8 0x564b7e3ae141 <unknown>
15:18:43  #9 0x564b7e8872ab <unknown>
15:18:43  #10 0x564b7e88b0b9 <unknown>
15:18:43  #11 0x564b7e86e139 <unknown>
15:18:43  #12 0x564b7e88bc68 <unknown>
15:18:43  #13 0x564b7e85260f <unknown>
15:18:43  #14 0x564b7e8b01f8 <unknown>
15:18:43  #15 0x564b7e8b03d6 <unknown>
15:18:43  #16 0x564b7e8c15e6 <unknown>
15:18:43  #17 0x7f4d7efcb609 start_thread
15:18:43  ------------------------------------------------------------------------------
15:18:43  Tcs 11: Func-Course: Create Course with maximum credits value :: K... | FAIL |
15:18:43  Parent suite setup failed:
15:18:43  SessionNotCreatedException: Message: session not created: probably user data directory is already in use, please specify a unique value for --user-data-dir argument, or don't use --user-data-dir
15:18:43  Stacktrace:
15:18:43  #0 0x564b7e8c22ca <unknown>
15:18:43  #1 0x564b7e369550 <unknown>
15:18:43  #2 0x564b7e3a39cb <unknown>
15:18:43  #3 0x564b7e39ed17 <unknown>
15:18:43  #4 0x564b7e3ef19e <unknown>
15:18:43  #5 0x564b7e3ee766 <unknown>
15:18:43  #6 0x564b7e3e0993 <unknown>
15:18:43  #7 0x564b7e3acd6b <unknown>
15:18:43  #8 0x564b7e3ae141 <unknown>
15:18:43  #9 0x564b7e8872ab <unknown>
15:18:43  #10 0x564b7e88b0b9 <unknown>
15:18:43  #11 0x564b7e86e139 <unknown>
15:18:43  #12 0x564b7e88bc68 <unknown>
15:18:43  #13 0x564b7e85260f <unknown>
15:18:43  #14 0x564b7e8b01f8 <unknown>
15:18:43  #15 0x564b7e8b03d6 <unknown>
15:18:43  #16 0x564b7e8c15e6 <unknown>
15:18:43  #17 0x7f4d7efcb609 start_thread
15:18:43  ------------------------------------------------------------------------------
15:18:43  Tcs 03: Func-CLO-03: Create CLOs with fully fields :: Kiểm tra việ... | FAIL |
15:18:43  Parent suite setup failed:
15:18:43  SessionNotCreatedException: Message: session not created: probably user data directory is already in use, please specify a unique value for --user-data-dir argument, or don't use --user-data-dir
15:18:43  Stacktrace:
15:18:43  #0 0x564b7e8c22ca <unknown>
15:18:43  #1 0x564b7e369550 <unknown>
15:18:43  #2 0x564b7e3a39cb <unknown>
15:18:43  #3 0x564b7e39ed17 <unknown>
15:18:43  #4 0x564b7e3ef19e <unknown>
15:18:43  #5 0x564b7e3ee766 <unknown>
15:18:43  #6 0x564b7e3e0993 <unknown>
15:18:43  #7 0x564b7e3acd6b <unknown>
15:18:43  #8 0x564b7e3ae141 <unknown>
15:18:43  #9 0x564b7e8872ab <unknown>
15:18:43  #10 0x564b7e88b0b9 <unknown>
15:18:43  #11 0x564b7e86e139 <unknown>
15:18:43  #12 0x564b7e88bc68 <unknown>
15:18:43  #13 0x564b7e85260f <unknown>
15:18:43  #14 0x564b7e8b01f8 <unknown>
15:18:43  #15 0x564b7e8b03d6 <unknown>
15:18:43  #16 0x564b7e8c15e6 <unknown>
15:18:43  #17 0x7f4d7efcb609 start_thread
15:18:43  ------------------------------------------------------------------------------
15:18:43  Tcs 04: Func-CLO-04: Create CLOS with empty required fields :: Tạo... | FAIL |
15:18:43  Parent suite setup failed:
15:18:43  SessionNotCreatedException: Message: session not created: probably user data directory is already in use, please specify a unique value for --user-data-dir argument, or don't use --user-data-dir
15:18:43  Stacktrace:
15:18:43  #0 0x564b7e8c22ca <unknown>
15:18:43  #1 0x564b7e369550 <unknown>
15:18:43  #2 0x564b7e3a39cb <unknown>
15:18:43  #3 0x564b7e39ed17 <unknown>
15:18:43  #4 0x564b7e3ef19e <unknown>
15:18:43  #5 0x564b7e3ee766 <unknown>
15:18:43  #6 0x564b7e3e0993 <unknown>
15:18:43  #7 0x564b7e3acd6b <unknown>
15:18:43  #8 0x564b7e3ae141 <unknown>
15:18:43  #9 0x564b7e8872ab <unknown>
15:18:43  #10 0x564b7e88b0b9 <unknown>
15:18:43  #11 0x564b7e86e139 <unknown>
15:18:43  #12 0x564b7e88bc68 <unknown>
15:18:43  #13 0x564b7e85260f <unknown>
15:18:43  #14 0x564b7e8b01f8 <unknown>
15:18:43  #15 0x564b7e8b03d6 <unknown>
15:18:43  #16 0x564b7e8c15e6 <unknown>
15:18:43  #17 0x7f4d7efcb609 start_thread
15:18:43  ------------------------------------------------------------------------------
15:18:43  Tcs 06: Edit Course :: Chỉnh sửa thông tin khoá học đã tạo            | FAIL |
15:18:43  Parent suite setup failed:
15:18:43  SessionNotCreatedException: Message: session not created: probably user data directory is already in use, please specify a unique value for --user-data-dir argument, or don't use --user-data-dir
15:18:43  Stacktrace:
15:18:43  #0 0x564b7e8c22ca <unknown>
15:18:43  #1 0x564b7e369550 <unknown>
15:18:43  #2 0x564b7e3a39cb <unknown>
15:18:43  #3 0x564b7e39ed17 <unknown>
15:18:43  #4 0x564b7e3ef19e <unknown>
15:18:43  #5 0x564b7e3ee766 <unknown>
15:18:43  #6 0x564b7e3e0993 <unknown>
15:18:43  #7 0x564b7e3acd6b <unknown>
15:18:43  #8 0x564b7e3ae141 <unknown>
15:18:43  #9 0x564b7e8872ab <unknown>
15:18:43  #10 0x564b7e88b0b9 <unknown>
15:18:43  #11 0x564b7e86e139 <unknown>
15:18:43  #12 0x564b7e88bc68 <unknown>
15:18:43  #13 0x564b7e85260f <unknown>
15:18:43  #14 0x564b7e8b01f8 <unknown>
15:18:43  #15 0x564b7e8b03d6 <unknown>
15:18:43  #16 0x564b7e8c15e6 <unknown>
15:18:43  #17 0x7f4d7efcb609 start_thread
15:18:43  ------------------------------------------------------------------------------
15:18:43  Tcs 08: Compare value CLO in course :: So sánh giá trị CLO đã định... | FAIL |
15:18:43  Parent suite setup failed:
15:18:43  SessionNotCreatedException: Message: session not created: probably user data directory is already in use, please specify a unique value for --user-data-dir argument, or don't use --user-data-dir
15:18:43  Stacktrace:
15:18:43  #0 0x564b7e8c22ca <unknown>
15:18:43  #1 0x564b7e369550 <unknown>
15:18:43  #2 0x564b7e3a39cb <unknown>
15:18:43  #3 0x564b7e39ed17 <unknown>
15:18:43  #4 0x564b7e3ef19e <unknown>
15:18:43  #5 0x564b7e3ee766 <unknown>
15:18:43  #6 0x564b7e3e0993 <unknown>
15:18:43  #7 0x564b7e3acd6b <unknown>
15:18:43  #8 0x564b7e3ae141 <unknown>
15:18:43  #9 0x564b7e8872ab <unknown>
15:18:43  #10 0x564b7e88b0b9 <unknown>
15:18:43  #11 0x564b7e86e139 <unknown>
15:18:43  #12 0x564b7e88bc68 <unknown>
15:18:43  #13 0x564b7e85260f <unknown>
15:18:43  #14 0x564b7e8b01f8 <unknown>
15:18:43  #15 0x564b7e8b03d6 <unknown>
15:18:43  #16 0x564b7e8c15e6 <unknown>
15:18:43  #17 0x7f4d7efcb609 start_thread
15:18:43  ------------------------------------------------------------------------------
15:18:43  TC 09: Edit All Fields In Course :: Kiểm tra chỉnh sửa giá trị vào... | FAIL |
15:18:43  Parent suite setup failed:
15:18:43  SessionNotCreatedException: Message: session not created: probably user data directory is already in use, please specify a unique value for --user-data-dir argument, or don't use --user-data-dir
15:18:43  Stacktrace:
15:18:43  #0 0x564b7e8c22ca <unknown>
15:18:43  #1 0x564b7e369550 <unknown>
15:18:43  #2 0x564b7e3a39cb <unknown>
15:18:43  #3 0x564b7e39ed17 <unknown>
15:18:43  #4 0x564b7e3ef19e <unknown>
15:18:43  #5 0x564b7e3ee766 <unknown>
15:18:43  #6 0x564b7e3e0993 <unknown>
15:18:43  #7 0x564b7e3acd6b <unknown>
15:18:43  #8 0x564b7e3ae141 <unknown>
15:18:43  #9 0x564b7e8872ab <unknown>
15:18:43  #10 0x564b7e88b0b9 <unknown>
15:18:43  #11 0x564b7e86e139 <unknown>
15:18:43  #12 0x564b7e88bc68 <unknown>
15:18:43  #13 0x564b7e85260f <unknown>
15:18:43  #14 0x564b7e8b01f8 <unknown>
15:18:43  #15 0x564b7e8b03d6 <unknown>
15:18:43  #16 0x564b7e8c15e6 <unknown>
15:18:43  #17 0x7f4d7efcb609 start_thread
15:18:43  ------------------------------------------------------------------------------
15:18:43  Tcs 12: "Tạo mới" button inactive :: Kiểm tra nút "Tạo mới" không ... | FAIL |
15:18:43  Parent suite setup failed:
15:18:43  SessionNotCreatedException: Message: session not created: probably user data directory is already in use, please specify a unique value for --user-data-dir argument, or don't use --user-data-dir
15:18:43  Stacktrace:
15:18:43  #0 0x564b7e8c22ca <unknown>
15:18:43  #1 0x564b7e369550 <unknown>
15:18:43  #2 0x564b7e3a39cb <unknown>
15:18:43  #3 0x564b7e39ed17 <unknown>
15:18:43  #4 0x564b7e3ef19e <unknown>
15:18:43  #5 0x564b7e3ee766 <unknown>
15:18:43  #6 0x564b7e3e0993 <unknown>
15:18:43  #7 0x564b7e3acd6b <unknown>
15:18:43  #8 0x564b7e3ae141 <unknown>
15:18:43  #9 0x564b7e8872ab <unknown>
15:18:43  #10 0x564b7e88b0b9 <unknown>
15:18:43  #11 0x564b7e86e139 <unknown>
15:18:43  #12 0x564b7e88bc68 <unknown>
15:18:43  #13 0x564b7e85260f <unknown>
15:18:43  #14 0x564b7e8b01f8 <unknown>
15:18:43  #15 0x564b7e8b03d6 <unknown>
15:18:43  #16 0x564b7e8c15e6 <unknown>
15:18:43  #17 0x7f4d7efcb609 start_thread
15:18:43  ------------------------------------------------------------------------------
15:18:43  Tc 13: Find word with valid and invalid Keyword :: Tìm kiếm từ kho... | FAIL |
15:18:43  Parent suite setup failed:
15:18:43  SessionNotCreatedException: Message: session not created: probably user data directory is already in use, please specify a unique value for --user-data-dir argument, or don't use --user-data-dir
15:18:43  Stacktrace:
15:18:43  #0 0x564b7e8c22ca <unknown>
15:18:43  #1 0x564b7e369550 <unknown>
15:18:43  #2 0x564b7e3a39cb <unknown>
15:18:43  #3 0x564b7e39ed17 <unknown>
15:18:43  #4 0x564b7e3ef19e <unknown>
15:18:43  #5 0x564b7e3ee766 <unknown>
15:18:43  #6 0x564b7e3e0993 <unknown>
15:18:43  #7 0x564b7e3acd6b <unknown>
15:18:43  #8 0x564b7e3ae141 <unknown>
15:18:43  #9 0x564b7e8872ab <unknown>
15:18:43  #10 0x564b7e88b0b9 <unknown>
15:18:43  #11 0x564b7e86e139 <unknown>
15:18:43  #12 0x564b7e88bc68 <unknown>
15:18:43  #13 0x564b7e85260f <unknown>
15:18:43  #14 0x564b7e8b01f8 <unknown>
15:18:43  #15 0x564b7e8b03d6 <unknown>
15:18:43  #16 0x564b7e8c15e6 <unknown>
15:18:43  #17 0x7f4d7efcb609 start_thread
15:18:43  ------------------------------------------------------------------------------
15:18:43  Test.Course Test                                                      | FAIL |
15:18:43  Suite setup failed:
15:18:43  SessionNotCreatedException: Message: session not created: probably user data directory is already in use, please specify a unique value for --user-data-dir argument, or don't use --user-data-dir
15:18:43  Stacktrace:
15:18:43  #0 0x564b7e8c22ca <unknown>
15:18:43  #1 0x564b7e369550 <unknown>
15:18:43  #2 0x564b7e3a39cb <unknown>
15:18:43  #3 0x564b7e39ed17 <unknown>
15:18:43  #4 0x564b7e3ef19e <unknown>
15:18:43  #5 0x564b7e3ee766 <unknown>
15:18:43  #6 0x564b7e3e0993 <unknown>
15:18:43  #7 0x564b7e3acd6b <unknown>
15:18:43  #8 0x564b7e3ae141 <unknown>
15:18:43  #9 0x564b7e8872ab <unknown>
15:18:43  #10 0x564b7e88b0b9 <unknown>
15:18:43  #11 0x564b7e86e139 <unknown>
15:18:43  #12 0x564b7e88bc68 <unknown>
15:18:43  #13 0x564b7e85260f <unknown>
15:18:43  #14 0x564b7e8b01f8 <unknown>
15:18:43  #15 0x564b7e8b03d6 <unknown>
15:18:43  #16 0x564b7e8c15e6 <unknown>
15:18:43  #17 0x7f4d7efcb609 start_thread
15:18:43  
15:18:43  
15:18:43  10 tests, 0 passed, 10 failed
15:18:43  ==============================================================================
15:18:43  Test.Create Question                                                          
15:18:43  ==============================================================================
15:18:43  VERIFY FORM CREATE QUESTION IS DISPLAYED :: Test verify form creat... | FAIL |
15:18:43  Setup failed:
15:18:43  SessionNotCreatedException: Message: session not created: probably user data directory is already in use, please specify a unique value for --user-data-dir argument, or don't use --user-data-dir
15:18:43  Stacktrace:
15:18:43  #0 0x5654f810d2ca <unknown>
15:18:43  #1 0x5654f7bb4550 <unknown>
15:18:43  #2 0x5654f7bee9cb <unknown>
15:18:43  #3 0x5654f7be9d17 <unknown>
15:18:43  #4 0x5654f7c3a19e <unknown>
15:18:43  #5 0x5654f7c39766 <unknown>
15:18:43  #6 0x5654f7c2b993 <unknown>
15:18:43  #7 0x5654f7bf7d6b <unknown>
15:18:43  #8 0x5654f7bf9141 <unknown>
15:18:43  #9 0x5654f80d22ab <unknown>
15:18:43  #10 0x5654f80d60b9 <unknown>
15:18:43  #11 0x5654f80b9139 <unknown>
15:18:43  #12 0x5654f80d6c68 <unknown>
15:18:43  #13 0x5654f809d60f <unknown>
15:18:43  #14 0x5654f80fb1f8 <unknown>
15:18:43  #15 0x5654f80fb3d6 <unknown>
15:18:43  #16 0x5654f810c5e6 <unknown>
15:18:43  #17 0x7fdcbc69c609 start_thread
15:18:43  ------------------------------------------------------------------------------
15:18:43  VERIFY DROPDOWN OPTION CREATE QUESTION FROM :: Test verify dropdow... | FAIL |
15:18:43  Setup failed:
15:18:43  SessionNotCreatedException: Message: session not created: probably user data directory is already in use, please specify a unique value for --user-data-dir argument, or don't use --user-data-dir
15:18:43  Stacktrace:
15:18:43  #0 0x55b03439f2ca <unknown>
15:18:43  #1 0x55b033e46550 <unknown>
15:18:43  #2 0x55b033e809cb <unknown>
15:18:43  #3 0x55b033e7bd17 <unknown>
15:18:43  #4 0x55b033ecc19e <unknown>
15:18:43  #5 0x55b033ecb766 <unknown>
15:18:43  #6 0x55b033ebd993 <unknown>
15:18:43  #7 0x55b033e89d6b <unknown>
15:18:43  #8 0x55b033e8b141 <unknown>
15:18:43  #9 0x55b0343642ab <unknown>
15:18:43  #10 0x55b0343680b9 <unknown>
15:18:43  #11 0x55b03434b139 <unknown>
15:18:43  #12 0x55b034368c68 <unknown>
15:18:43  #13 0x55b03432f60f <unknown>
15:18:43  #14 0x55b03438d1f8 <unknown>
15:18:43  #15 0x55b03438d3d6 <unknown>
15:18:43  #16 0x55b03439e5e6 <unknown>
15:18:43  #17 0x7f3b621d7609 start_thread
15:18:43  ------------------------------------------------------------------------------
15:18:43  VERIFY REQUIRED FIELD ERRORS :: Test verify required field error m... | FAIL |
15:18:43  Setup failed:
15:18:43  SessionNotCreatedException: Message: session not created: probably user data directory is already in use, please specify a unique value for --user-data-dir argument, or don't use --user-data-dir
15:18:43  Stacktrace:
15:18:43  #0 0x560e447ac2ca <unknown>
15:18:43  #1 0x560e44253550 <unknown>
15:18:43  #2 0x560e4428d9cb <unknown>
15:18:43  #3 0x560e44288d17 <unknown>
15:18:43  #4 0x560e442d919e <unknown>
15:18:43  #5 0x560e442d8766 <unknown>
15:18:43  #6 0x560e442ca993 <unknown>
15:18:43  #7 0x560e44296d6b <unknown>
15:18:43  #8 0x560e44298141 <unknown>
15:18:43  #9 0x560e447712ab <unknown>
15:18:43  #10 0x560e447750b9 <unknown>
15:18:43  #11 0x560e44758139 <unknown>
15:18:43  #12 0x560e44775c68 <unknown>
15:18:43  #13 0x560e4473c60f <unknown>
15:18:43  #14 0x560e4479a1f8 <unknown>
15:18:43  #15 0x560e4479a3d6 <unknown>
15:18:43  #16 0x560e447ab5e6 <unknown>
15:18:43  #17 0x7f72678c5609 start_thread
15:18:43  ------------------------------------------------------------------------------
15:18:43  VERIFY ANSWER FORM :: Test verify answer form appears when Multipl... | FAIL |
15:18:43  Setup failed:
15:18:43  SessionNotCreatedException: Message: session not created: probably user data directory is already in use, please specify a unique value for --user-data-dir argument, or don't use --user-data-dir
15:18:43  Stacktrace:
15:18:43  #0 0x5637404232ca <unknown>
15:18:43  #1 0x56373feca550 <unknown>
15:18:43  #2 0x56373ff049cb <unknown>
15:18:43  #3 0x56373feffd17 <unknown>
15:18:43  #4 0x56373ff5019e <unknown>
15:18:43  #5 0x56373ff4f766 <unknown>
15:18:43  #6 0x56373ff41993 <unknown>
15:18:43  #7 0x56373ff0dd6b <unknown>
15:18:43  #8 0x56373ff0f141 <unknown>
15:18:43  #9 0x5637403e82ab <unknown>
15:18:43  #10 0x5637403ec0b9 <unknown>
15:18:43  #11 0x5637403cf139 <unknown>
15:18:43  #12 0x5637403ecc68 <unknown>
15:18:43  #13 0x5637403b360f <unknown>
15:18:43  #14 0x5637404111f8 <unknown>
15:18:43  #15 0x5637404113d6 <unknown>
15:18:43  #16 0x5637404225e6 <unknown>
15:18:43  #17 0x7efde7d16609 start_thread
15:18:43  ------------------------------------------------------------------------------
15:18:43  CREATE SIMPLE QUESTION FORM :: Test create a simple question          | FAIL |
15:18:43  Setup failed:
15:18:43  SessionNotCreatedException: Message: session not created: probably user data directory is already in use, please specify a unique value for --user-data-dir argument, or don't use --user-data-dir
15:18:43  Stacktrace:
15:18:43  #0 0x55e55aeda2ca <unknown>
15:18:43  #1 0x55e55a981550 <unknown>
15:18:43  #2 0x55e55a9bb9cb <unknown>
15:18:43  #3 0x55e55a9b6d17 <unknown>
15:18:43  #4 0x55e55aa0719e <unknown>
15:18:43  #5 0x55e55aa06766 <unknown>
15:18:43  #6 0x55e55a9f8993 <unknown>
15:18:43  #7 0x55e55a9c4d6b <unknown>
15:18:43  #8 0x55e55a9c6141 <unknown>
15:18:43  #9 0x55e55ae9f2ab <unknown>
15:18:43  #10 0x55e55aea30b9 <unknown>
15:18:43  #11 0x55e55ae86139 <unknown>
15:18:43  #12 0x55e55aea3c68 <unknown>
15:18:43  #13 0x55e55ae6a60f <unknown>
15:18:43  #14 0x55e55aec81f8 <unknown>
15:18:43  #15 0x55e55aec83d6 <unknown>
15:18:43  #16 0x55e55aed95e6 <unknown>
15:18:43  #17 0x7f8b16253609 start_thread
15:18:43  ------------------------------------------------------------------------------
15:18:43  Test.Create Question                                                  | FAIL |
15:18:43  5 tests, 0 passed, 5 failed
15:18:43  ==============================================================================
15:18:43  Test.Login Tests                                                              
15:18:43  ==============================================================================
15:18:43  Verify Login With User Admin :: Test case to verify valid login wi... | FAIL |
15:18:43  SessionNotCreatedException: Message: session not created: probably user data directory is already in use, please specify a unique value for --user-data-dir argument, or don't use --user-data-dir
15:18:43  Stacktrace:
15:18:43  #0 0x55764f2b42ca <unknown>
15:18:43  #1 0x55764ed5b550 <unknown>
15:18:43  #2 0x55764ed959cb <unknown>
15:18:43  #3 0x55764ed90d17 <unknown>
15:18:43  #4 0x55764ede119e <unknown>
15:18:43  #5 0x55764ede0766 <unknown>
15:18:43  #6 0x55764edd2993 <unknown>
15:18:43  #7 0x55764ed9ed6b <unknown>
15:18:43  #8 0x55764eda0141 <unknown>
15:18:43  #9 0x55764f2792ab <unknown>
15:18:43  #10 0x55764f27d0b9 <unknown>
15:18:43  #11 0x55764f260139 <unknown>
15:18:43  #12 0x55764f27dc68 <unknown>
15:18:43  #13 0x55764f24460f <unknown>
15:18:43  #14 0x55764f2a21f8 <unknown>
15:18:43  #15 0x55764f2a23d6 <unknown>
15:18:43  #16 0x55764f2b35e6 <unknown>
15:18:43  #17 0x7ff97b3e9609 start_thread
15:18:43  ------------------------------------------------------------------------------
15:18:43  Verify Login With User Lecturer :: Test case to verify valid login... | FAIL |
15:18:43  SessionNotCreatedException: Message: session not created: probably user data directory is already in use, please specify a unique value for --user-data-dir argument, or don't use --user-data-dir
15:18:43  Stacktrace:
15:18:43  #0 0x562c4ac552ca <unknown>
15:18:43  #1 0x562c4a6fc550 <unknown>
15:18:43  #2 0x562c4a7369cb <unknown>
15:18:43  #3 0x562c4a731d17 <unknown>
15:18:43  #4 0x562c4a78219e <unknown>
15:18:43  #5 0x562c4a781766 <unknown>
15:18:43  #6 0x562c4a773993 <unknown>
15:18:43  #7 0x562c4a73fd6b <unknown>
15:18:43  #8 0x562c4a741141 <unknown>
15:18:43  #9 0x562c4ac1a2ab <unknown>
15:18:43  #10 0x562c4ac1e0b9 <unknown>
15:18:43  #11 0x562c4ac01139 <unknown>
15:18:43  #12 0x562c4ac1ec68 <unknown>
15:18:43  #13 0x562c4abe560f <unknown>
15:18:43  #14 0x562c4ac431f8 <unknown>
15:18:43  #15 0x562c4ac433d6 <unknown>
15:18:43  #16 0x562c4ac545e6 <unknown>
15:18:43  #17 0x7fb0a4cf3609 start_thread
15:18:43  ------------------------------------------------------------------------------
15:18:43  Verify Login With Invalid User :: Test case to verify invalid login   | FAIL |
15:18:43  SessionNotCreatedException: Message: session not created: probably user data directory is already in use, please specify a unique value for --user-data-dir argument, or don't use --user-data-dir
15:18:43  Stacktrace:
15:18:43  #0 0x55bc8a3f72ca <unknown>
15:18:43  #1 0x55bc89e9e550 <unknown>
15:18:43  #2 0x55bc89ed89cb <unknown>
15:18:43  #3 0x55bc89ed3d17 <unknown>
15:18:43  #4 0x55bc89f2419e <unknown>
15:18:43  #5 0x55bc89f23766 <unknown>
15:18:43  #6 0x55bc89f15993 <unknown>
15:18:43  #7 0x55bc89ee1d6b <unknown>
15:18:43  #8 0x55bc89ee3141 <unknown>
15:18:43  #9 0x55bc8a3bc2ab <unknown>
15:18:43  #10 0x55bc8a3c00b9 <unknown>
15:18:43  #11 0x55bc8a3a3139 <unknown>
15:18:43  #12 0x55bc8a3c0c68 <unknown>
15:18:43  #13 0x55bc8a38760f <unknown>
15:18:43  #14 0x55bc8a3e51f8 <unknown>
15:18:43  #15 0x55bc8a3e53d6 <unknown>
15:18:43  #16 0x55bc8a3f65e6 <unknown>
15:18:43  #17 0x7fd5258c2609 start_thread
15:18:43  ------------------------------------------------------------------------------
15:18:43  Verify Login With Multiple Accounts From Excel :: Kiểm tra đăng nh... | FAIL |
15:18:43  Variable '@{ROBOT_USERS_PANDAS}' not found.
15:18:43  ------------------------------------------------------------------------------
15:18:43  Test.Login Tests                                                      | FAIL |
15:18:43  4 tests, 0 passed, 4 failed
15:18:43  ==============================================================================
15:18:43  Test.Program Test :: Page Object in Robot Framework                           
15:18:43  ==============================================================================
15:18:43  Verify successful create new program :: This test case verifies cr... | FAIL |
15:18:43  SessionNotCreatedException: Message: session not created: probably user data directory is already in use, please specify a unique value for --user-data-dir argument, or don't use --user-data-dir
15:18:43  Stacktrace:
15:18:43  #0 0x56020a5342ca <unknown>
15:18:43  #1 0x560209fdb550 <unknown>
15:18:43  #2 0x56020a0159cb <unknown>
15:18:43  #3 0x56020a010d17 <unknown>
15:18:43  #4 0x56020a06119e <unknown>
15:18:43  #5 0x56020a060766 <unknown>
15:18:43  #6 0x56020a052993 <unknown>
15:18:43  #7 0x56020a01ed6b <unknown>
15:18:43  #8 0x56020a020141 <unknown>
15:18:43  #9 0x56020a4f92ab <unknown>
15:18:43  #10 0x56020a4fd0b9 <unknown>
15:18:43  #11 0x56020a4e0139 <unknown>
15:18:43  #12 0x56020a4fdc68 <unknown>
15:18:43  #13 0x56020a4c460f <unknown>
15:18:43  #14 0x56020a5221f8 <unknown>
15:18:43  #15 0x56020a5223d6 <unknown>
15:18:43  #16 0x56020a5335e6 <unknown>
15:18:43  #17 0x7f424704a609 start_thread
15:18:43  ------------------------------------------------------------------------------
15:18:43  Verify create program fails when all fields are empty :: This test... | FAIL |
15:18:43  SessionNotCreatedException: Message: session not created: probably user data directory is already in use, please specify a unique value for --user-data-dir argument, or don't use --user-data-dir
15:18:43  Stacktrace:
15:18:43  #0 0x5585339e42ca <unknown>
15:18:43  #1 0x55853348b550 <unknown>
15:18:43  #2 0x5585334c59cb <unknown>
15:18:43  #3 0x5585334c0d17 <unknown>
15:18:43  #4 0x55853351119e <unknown>
15:18:43  #5 0x558533510766 <unknown>
15:18:43  #6 0x558533502993 <unknown>
15:18:43  #7 0x5585334ced6b <unknown>
15:18:43  #8 0x5585334d0141 <unknown>
15:18:43  #9 0x5585339a92ab <unknown>
15:18:43  #10 0x5585339ad0b9 <unknown>
15:18:43  #11 0x558533990139 <unknown>
15:18:43  #12 0x5585339adc68 <unknown>
15:18:43  #13 0x55853397460f <unknown>
15:18:43  #14 0x5585339d21f8 <unknown>
15:18:43  #15 0x5585339d23d6 <unknown>
15:18:43  #16 0x5585339e35e6 <unknown>
15:18:43  #17 0x7f31e17bd609 start_thread
15:18:43  ------------------------------------------------------------------------------
15:18:43  Verify go to Program page :: Verify go to Program page                | FAIL |
15:18:43  SessionNotCreatedException: Message: session not created: probably user data directory is already in use, please specify a unique value for --user-data-dir argument, or don't use --user-data-dir
15:18:43  Stacktrace:
15:18:43  #0 0x561ed12e12ca <unknown>
15:18:43  #1 0x561ed0d88550 <unknown>
15:18:43  #2 0x561ed0dc29cb <unknown>
15:18:43  #3 0x561ed0dbdd17 <unknown>
15:18:43  #4 0x561ed0e0e19e <unknown>
15:18:43  #5 0x561ed0e0d766 <unknown>
15:18:43  #6 0x561ed0dff993 <unknown>
15:18:43  #7 0x561ed0dcbd6b <unknown>
15:18:43  #8 0x561ed0dcd141 <unknown>
15:18:43  #9 0x561ed12a62ab <unknown>
15:18:43  #10 0x561ed12aa0b9 <unknown>
15:18:43  #11 0x561ed128d139 <unknown>
15:18:43  #12 0x561ed12aac68 <unknown>
15:18:43  #13 0x561ed127160f <unknown>
15:18:43  #14 0x561ed12cf1f8 <unknown>
15:18:43  #15 0x561ed12cf3d6 <unknown>
15:18:43  #16 0x561ed12e05e6 <unknown>
15:18:43  #17 0x7f4f1821c609 start_thread
15:18:43  ------------------------------------------------------------------------------
15:18:43  Search for program by Name :: Verify that the program search retur... | FAIL |
15:18:43  SessionNotCreatedException: Message: session not created: probably user data directory is already in use, please specify a unique value for --user-data-dir argument, or don't use --user-data-dir
15:18:43  Stacktrace:
15:18:43  #0 0x564c4b5712ca <unknown>
15:18:43  #1 0x564c4b018550 <unknown>
15:18:43  #2 0x564c4b0529cb <unknown>
15:18:43  #3 0x564c4b04dd17 <unknown>
15:18:43  #4 0x564c4b09e19e <unknown>
15:18:43  #5 0x564c4b09d766 <unknown>
15:18:43  #6 0x564c4b08f993 <unknown>
15:18:43  #7 0x564c4b05bd6b <unknown>
15:18:43  #8 0x564c4b05d141 <unknown>
15:18:43  #9 0x564c4b5362ab <unknown>
15:18:43  #10 0x564c4b53a0b9 <unknown>
15:18:43  #11 0x564c4b51d139 <unknown>
15:18:43  #12 0x564c4b53ac68 <unknown>
15:18:43  #13 0x564c4b50160f <unknown>
15:18:43  #14 0x564c4b55f1f8 <unknown>
15:18:43  #15 0x564c4b55f3d6 <unknown>
15:18:43  #16 0x564c4b5705e6 <unknown>
15:18:43  #17 0x7fb4f7708609 start_thread
15:18:43  ------------------------------------------------------------------------------
15:18:43  Search for program not found :: Verify that the program search ret... | FAIL |
15:18:43  SessionNotCreatedException: Message: session not created: probably user data directory is already in use, please specify a unique value for --user-data-dir argument, or don't use --user-data-dir
15:18:43  Stacktrace:
15:18:43  #0 0x564c06d2f2ca <unknown>
15:18:43  #1 0x564c067d6550 <unknown>
15:18:43  #2 0x564c068109cb <unknown>
15:18:43  #3 0x564c0680bd17 <unknown>
15:18:43  #4 0x564c0685c19e <unknown>
15:18:43  #5 0x564c0685b766 <unknown>
15:18:43  #6 0x564c0684d993 <unknown>
15:18:43  #7 0x564c06819d6b <unknown>
15:18:43  #8 0x564c0681b141 <unknown>
15:18:43  #9 0x564c06cf42ab <unknown>
15:18:43  #10 0x564c06cf80b9 <unknown>
15:18:43  #11 0x564c06cdb139 <unknown>
15:18:43  #12 0x564c06cf8c68 <unknown>
15:18:43  #13 0x564c06cbf60f <unknown>
15:18:43  #14 0x564c06d1d1f8 <unknown>
15:18:43  #15 0x564c06d1d3d6 <unknown>
15:18:43  #16 0x564c06d2e5e6 <unknown>
15:18:43  #17 0x7f594c8d8609 start_thread
15:18:43  ------------------------------------------------------------------------------
15:18:43  Test.Program Test :: Page Object in Robot Framework                   | FAIL |
15:18:43  5 tests, 0 passed, 5 failed
15:18:43  ==============================================================================
15:18:43  Test                                                                  | FAIL |
15:18:43  34 tests, 0 passed, 34 failed
15:18:43  ==============================================================================
15:18:43  Output:  /var/lib/jenkins/workspace/v-job/results/output.xml
15:18:43  Log:     /var/lib/jenkins/workspace/v-job/results/log.html
15:18:43  Report:  /var/lib/jenkins/workspace/v-job/results/report.html
15:18:43  
15:18:43  STDERR:
15:18:43  [ ERROR ] Error in file '/var/lib/jenkins/workspace/v-job/test/chapter_test.robot' on line 12: Processing variable file '/var/lib/jenkins/workspace/v-job/libs/data_reader.py' failed: Importing variable file '/var/lib/jenkins/workspace/v-job/libs/data_reader.py' failed: ModuleNotFoundError: No module named 'pandas'
15:18:43  Traceback (most recent call last):
15:18:43    File "/var/lib/jenkins/workspace/v-job/libs/data_reader.py", line 2, in <module>
15:18:43      import pandas as pd
15:18:43  PYTHONPATH:
15:18:43    /var/lib/jenkins/workspace/v-job/libs
15:18:43    /var/lib/jenkins/.local/bin
15:18:43    /usr/lib/python38.zip
15:18:43    /usr/lib/python3.8
15:18:43    /usr/lib/python3.8/lib-dynload
15:18:43    /var/lib/jenkins/.local/lib/python3.8/site-packages
15:18:43    /usr/local/lib/python3.8/dist-packages
15:18:43    /usr/lib/python3/dist-packages
15:18:43  [ ERROR ] Error in file '/var/lib/jenkins/workspace/v-job/test/course_test.robot' on line 14: Processing variable file '/var/lib/jenkins/workspace/v-job/libs/data_reader.py' failed: Importing variable file '/var/lib/jenkins/workspace/v-job/libs/data_reader.py' failed: ModuleNotFoundError: No module named 'pandas'
15:18:43  Traceback (most recent call last):
15:18:43    File "/var/lib/jenkins/workspace/v-job/libs/data_reader.py", line 2, in <module>
15:18:43      import pandas as pd
15:18:43  PYTHONPATH:
15:18:43    /var/lib/jenkins/workspace/v-job/libs
15:18:43    /var/lib/jenkins/.local/bin
15:18:43    /usr/lib/python38.zip
15:18:43    /usr/lib/python3.8
15:18:43    /usr/lib/python3.8/lib-dynload
15:18:43    /var/lib/jenkins/.local/lib/python3.8/site-packages
15:18:43    /usr/local/lib/python3.8/dist-packages
15:18:43    /usr/lib/python3/dist-packages
15:18:43  [ ERROR ] Error in file '/var/lib/jenkins/workspace/v-job/test/login_tests.robot' on line 6: Processing variable file '/var/lib/jenkins/workspace/v-job/libs/data_reader.py' failed: Importing variable file '/var/lib/jenkins/workspace/v-job/libs/data_reader.py' failed: ModuleNotFoundError: No module named 'pandas'
15:18:43  Traceback (most recent call last):
15:18:43    File "/var/lib/jenkins/workspace/v-job/libs/data_reader.py", line 2, in <module>
15:18:43      import pandas as pd
15:18:43  PYTHONPATH:
15:18:43    /var/lib/jenkins/workspace/v-job/libs
15:18:43    /var/lib/jenkins/.local/bin
15:18:43    /usr/lib/python38.zip
15:18:43    /usr/lib/python3.8
15:18:43    /usr/lib/python3.8/lib-dynload
15:18:43    /var/lib/jenkins/.local/lib/python3.8/site-packages
15:18:43    /usr/local/lib/python3.8/dist-packages
15:18:43    /usr/lib/python3/dist-packages
15:18:43  
15:18:43  Collecting test results...
15:18:43  Test Results Summary:
15:18:43    Total: 34
15:18:43    Passed: 0
15:18:43    Failed: 34
15:18:43    Success Rate: 0.00%
15:18:43    Overall Status: FAIL
15:18:43    Duration: N/A
15:18:43  
15:18:43  Failed Tests:
15:18:43    - Verify Form Add Assessment is Displayed: Setup failed:
15:18:43  SessionNotCreatedException: Message: session not created: probably user data directory is already in use, please specify a unique value for --user-data-dir argument, or don't use --user-data-dir
15:18:43  Stacktrace:
15:18:43  #0 0x56205219f2ca <unknown>
15:18:43  #1 0x562051c46550 <unknown>
15:18:43  #2 0x562051c809cb <unknown>
15:18:43  #3 0x562051c7bd17 <unknown>
15:18:43  #4 0x562051ccc19e <unknown>
15:18:43  #5 0x562051ccb766 <unknown>
15:18:43  #6 0x562051cbd993 <unknown>
15:18:43  #7 0x562051c89d6b <unknown>
15:18:43  #8 0x562051c8b141 <unknown>
15:18:43  #9 0x5620521642ab <unknown>
15:18:43  #10 0x5620521680b9 <unknown>
15:18:43  #11 0x56205214b139 <unknown>
15:18:43  #12 0x562052168c68 <unknown>
15:18:43  #13 0x56205212f60f <unknown>
15:18:43  #14 0x56205218d1f8 <unknown>
15:18:43  #15 0x56205218d3d6 <unknown>
15:18:43  #16 0x56205219e5e6 <unknown>
15:18:43  #17 0x7f27a822e609 start_thread
15:18:43  
15:18:43    - Verify Add Assessment Successfully: Setup failed:
15:18:43  SessionNotCreatedException: Message: session not created: probably user data directory is already in use, please specify a unique value for --user-data-dir argument, or don't use --user-data-dir
15:18:43  Stacktrace:
15:18:43  #0 0x556a68d052ca <unknown>
15:18:43  #1 0x556a687ac550 <unknown>
15:18:43  #2 0x556a687e69cb <unknown>
15:18:43  #3 0x556a687e1d17 <unknown>
15:18:43  #4 0x556a6883219e <unknown>
15:18:43  #5 0x556a68831766 <unknown>
15:18:43  #6 0x556a68823993 <unknown>
15:18:43  #7 0x556a687efd6b <unknown>
15:18:43  #8 0x556a687f1141 <unknown>
15:18:43  #9 0x556a68cca2ab <unknown>
15:18:43  #10 0x556a68cce0b9 <unknown>
15:18:43  #11 0x556a68cb1139 <unknown>
15:18:43  #12 0x556a68ccec68 <unknown>
15:18:43  #13 0x556a68c9560f <unknown>
15:18:43  #14 0x556a68cf31f8 <unknown>
15:18:43  #15 0x556a68cf33d6 <unknown>
15:18:43  #16 0x556a68d045e6 <unknown>
15:18:43  #17 0x7f9cce9d2609 start_thread
15:18:43  
15:18:43    - Verify Leave Required Fields Empty: Setup failed:
15:18:43  SessionNotCreatedException: Message: session not created: probably user data directory is already in use, please specify a unique value for --user-data-dir argument, or don't use --user-data-dir
15:18:43  Stacktrace:
15:18:43  #0 0x5580393242ca <unknown>
15:18:43  #1 0x558038dcb550 <unknown>
15:18:43  #2 0x558038e059cb <unknown>
15:18:43  #3 0x558038e00d17 <unknown>
15:18:43  #4 0x558038e5119e <unknown>
15:18:43  #5 0x558038e50766 <unknown>
15:18:43  #6 0x558038e42993 <unknown>
15:18:43  #7 0x558038e0ed6b <unknown>
15:18:43  #8 0x558038e10141 <unknown>
15:18:43  #9 0x5580392e92ab <unknown>
15:18:43  #10 0x5580392ed0b9 <unknown>
15:18:43  #11 0x5580392d0139 <unknown>
15:18:43  #12 0x5580392edc68 <unknown>
15:18:43  #13 0x5580392b460f <unknown>
15:18:43  #14 0x5580393121f8 <unknown>
15:18:43  #15 0x5580393123d6 <unknown>
15:18:43  #16 0x5580393235e6 <unknown>
15:18:43  #17 0x7f8bcae1d609 start_thread
15:18:43  
15:18:43    - Verify Fill Assessment With HighWeight: Setup failed:
15:18:43  SessionNotCreatedException: Message: session not created: probably user data directory is already in use, please specify a unique value for --user-data-dir argument, or don't use --user-data-dir
15:18:43  Stacktrace:
15:18:43  #0 0x55af0cfe52ca <unknown>
15:18:43  #1 0x55af0ca8c550 <unknown>
15:18:43  #2 0x55af0cac69cb <unknown>
15:18:43  #3 0x55af0cac1d17 <unknown>
15:18:43  #4 0x55af0cb1219e <unknown>
15:18:43  #5 0x55af0cb11766 <unknown>
15:18:43  #6 0x55af0cb03993 <unknown>
15:18:43  #7 0x55af0cacfd6b <unknown>
15:18:43  #8 0x55af0cad1141 <unknown>
15:18:43  #9 0x55af0cfaa2ab <unknown>
15:18:43  #10 0x55af0cfae0b9 <unknown>
15:18:43  #11 0x55af0cf91139 <unknown>
15:18:43  #12 0x55af0cfaec68 <unknown>
15:18:43  #13 0x55af0cf7560f <unknown>
15:18:43  #14 0x55af0cfd31f8 <unknown>
15:18:43  #15 0x55af0cfd33d6 <unknown>
15:18:43  #16 0x55af0cfe45e6 <unknown>
15:18:43  #17 0x7f5c65453609 start_thread
15:18:43  
15:18:43    - Verify Fill Assessment WithInvalidPassScore: Setup failed:
15:18:43  SessionNotCreatedException: Message: session not created: probably user data directory is already in use, please specify a unique value for --user-data-dir argument, or don't use --user-data-dir
15:18:43  Stacktrace:
15:18:43  #0 0x56239715d2ca <unknown>
15:18:43  #1 0x562396c04550 <unknown>
15:18:43  #2 0x562396c3e9cb <unknown>
15:18:43  #3 0x562396c39d17 <unknown>
15:18:43  #4 0x562396c8a19e <unknown>
15:18:43  #5 0x562396c89766 <unknown>
15:18:43  #6 0x562396c7b993 <unknown>
15:18:43  #7 0x562396c47d6b <unknown>
15:18:43  #8 0x562396c49141 <unknown>
15:18:43  #9 0x5623971222ab <unknown>
15:18:43  #10 0x5623971260b9 <unknown>
15:18:43  #11 0x562397109139 <unknown>
15:18:43  #12 0x562397126c68 <unknown>
15:18:43  #13 0x5623970ed60f <unknown>
15:18:43  #14 0x56239714b1f8 <unknown>
15:18:43  #15 0x56239714b3d6 <unknown>
15:18:43  #16 0x56239715c5e6 <unknown>
15:18:43  #17 0x7fb6d0ab3609 start_thread
15:18:43  
15:18:43    - Verify Fill Assessment WithSpecialDescription: Setup failed:
15:18:43  SessionNotCreatedException: Message: session not created: probably user data directory is already in use, please specify a unique value for --user-data-dir argument, or don't use --user-data-dir
15:18:43  Stacktrace:
15:18:43  #0 0x5584e4fb92ca <unknown>
15:18:43  #1 0x5584e4a60550 <unknown>
15:18:43  #2 0x5584e4a9a9cb <unknown>
15:18:43  #3 0x5584e4a95d17 <unknown>
15:18:43  #4 0x5584e4ae619e <unknown>
15:18:43  #5 0x5584e4ae5766 <unknown>
15:18:43  #6 0x5584e4ad7993 <unknown>
15:18:43  #7 0x5584e4aa3d6b <unknown>
15:18:43  #8 0x5584e4aa5141 <unknown>
15:18:43  #9 0x5584e4f7e2ab <unknown>
15:18:43  #10 0x5584e4f820b9 <unknown>
15:18:43  #11 0x5584e4f65139 <unknown>
15:18:43  #12 0x5584e4f82c68 <unknown>
15:18:43  #13 0x5584e4f4960f <unknown>
15:18:43  #14 0x5584e4fa71f8 <unknown>
15:18:43  #15 0x5584e4fa73d6 <unknown>
15:18:43  #16 0x5584e4fb85e6 <unknown>
15:18:43  #17 0x7fe5d6d09609 start_thread
15:18:43  
15:18:43    - TC_BrowserQs_001: Approve Question: Setup failed:
15:18:43  SessionNotCreatedException: Message: session not created: probably user data directory is already in use, please specify a unique value for --user-data-dir argument, or don't use --user-data-dir
15:18:43  Stacktrace:
15:18:43  #0 0x556a803e72ca <unknown>
15:18:43  #1 0x556a7fe8e550 <unknown>
15:18:43  #2 0x556a7fec89cb <unknown>
15:18:43  #3 0x556a7fec3d17 <unknown>
15:18:43  #4 0x556a7ff1419e <unknown>
15:18:43  #5 0x556a7ff13766 <unknown>
15:18:43  #6 0x556a7ff05993 <unknown>
15:18:43  #7 0x556a7fed1d6b <unknown>
15:18:43  #8 0x556a7fed3141 <unknown>
15:18:43  #9 0x556a803ac2ab <unknown>
15:18:43  #10 0x556a803b00b9 <unknown>
15:18:43  #11 0x556a80393139 <unknown>
15:18:43  #12 0x556a803b0c68 <unknown>
15:18:43  #13 0x556a8037760f <unknown>
15:18:43  #14 0x556a803d51f8 <unknown>
15:18:43  #15 0x556a803d53d6 <unknown>
15:18:43  #16 0x556a803e65e6 <unknown>
15:18:43  #17 0x7f7bf450d609 start_thread
15:18:43  
15:18:43    - TC_BrowserQs_002: Reject Question: Setup failed:
15:18:43  SessionNotCreatedException: Message: session not created: probably user data directory is already in use, please specify a unique value for --user-data-dir argument, or don't use --user-data-dir
15:18:43  Stacktrace:
15:18:43  #0 0x5633582f32ca <unknown>
15:18:43  #1 0x563357d9a550 <unknown>
15:18:43  #2 0x563357dd49cb <unknown>
15:18:43  #3 0x563357dcfd17 <unknown>
15:18:43  #4 0x563357e2019e <unknown>
15:18:43  #5 0x563357e1f766 <unknown>
15:18:43  #6 0x563357e11993 <unknown>
15:18:43  #7 0x563357dddd6b <unknown>
15:18:43  #8 0x563357ddf141 <unknown>
15:18:43  #9 0x5633582b82ab <unknown>
15:18:43  #10 0x5633582bc0b9 <unknown>
15:18:43  #11 0x56335829f139 <unknown>
15:18:43  #12 0x5633582bcc68 <unknown>
15:18:43  #13 0x56335828360f <unknown>
15:18:43  #14 0x5633582e11f8 <unknown>
15:18:43  #15 0x5633582e13d6 <unknown>
15:18:43  #16 0x5633582f25e6 <unknown>
15:18:43  #17 0x7f81651e9609 start_thread
15:18:43  
15:18:43    - TC_BrowserQs_003: Filter Course: Setup failed:
15:18:43  SessionNotCreatedException: Message: session not created: probably user data directory is already in use, please specify a unique value for --user-data-dir argument, or don't use --user-data-dir
15:18:43  Stacktrace:
15:18:43  #0 0x5645f7afb2ca <unknown>
15:18:43  #1 0x5645f75a2550 <unknown>
15:18:43  #2 0x5645f75dc9cb <unknown>
15:18:43  #3 0x5645f75d7d17 <unknown>
15:18:43  #4 0x5645f762819e <unknown>
15:18:43  #5 0x5645f7627766 <unknown>
15:18:43  #6 0x5645f7619993 <unknown>
15:18:43  #7 0x5645f75e5d6b <unknown>
15:18:43  #8 0x5645f75e7141 <unknown>
15:18:43  #9 0x5645f7ac02ab <unknown>
15:18:43  #10 0x5645f7ac40b9 <unknown>
15:18:43  #11 0x5645f7aa7139 <unknown>
15:18:43  #12 0x5645f7ac4c68 <unknown>
15:18:43  #13 0x5645f7a8b60f <unknown>
15:18:43  #14 0x5645f7ae91f8 <unknown>
15:18:43  #15 0x5645f7ae93d6 <unknown>
15:18:43  #16 0x5645f7afa5e6 <unknown>
15:18:43  #17 0x7f21848dc609 start_thread
15:18:43  
15:18:43    - Tcs 01: Func-Chapter: Create Chapter with fully field: Setup failed:
15:18:43  SessionNotCreatedException: Message: session not created: probably user data directory is already in use, please specify a unique value for --user-data-dir argument, or don't use --user-data-dir
15:18:43  Stacktrace:
15:18:43  #0 0x564ff61f72ca <unknown>
15:18:43  #1 0x564ff5c9e550 <unknown>
15:18:43  #2 0x564ff5cd89cb <unknown>
15:18:43  #3 0x564ff5cd3d17 <unknown>
15:18:43  #4 0x564ff5d2419e <unknown>
15:18:43  #5 0x564ff5d23766 <unknown>
15:18:43  #6 0x564ff5d15993 <unknown>
15:18:43  #7 0x564ff5ce1d6b <unknown>
15:18:43  #8 0x564ff5ce3141 <unknown>
15:18:43  #9 0x564ff61bc2ab <unknown>
15:18:43  #10 0x564ff61c00b9 <unknown>
15:18:43  #11 0x564ff61a3139 <unknown>
15:18:43  #12 0x564ff61c0c68 <unknown>
15:18:43  #13 0x564ff618760f <unknown>
15:18:43  #14 0x564ff61e51f8 <unknown>
15:18:43  #15 0x564ff61e53d6 <unknown>
15:18:43  #16 0x564ff61f65e6 <unknown>
15:18:43  #17 0x7f1d7cf7d609 start_thread
15:18:43  
15:18:43    - Tcs 01: Func-Course-03: Create Course with fully field: Parent suite setup failed:
15:18:43  SessionNotCreatedException: Message: session not created: probably user data directory is already in use, please specify a unique value for --user-data-dir argument, or don't use --user-data-dir
15:18:43  Stacktrace:
15:18:43  #0 0x564b7e8c22ca <unknown>
15:18:43  #1 0x564b7e369550 <unknown>
15:18:43  #2 0x564b7e3a39cb <unknown>
15:18:43  #3 0x564b7e39ed17 <unknown>
15:18:43  #4 0x564b7e3ef19e <unknown>
15:18:43  #5 0x564b7e3ee766 <unknown>
15:18:43  #6 0x564b7e3e0993 <unknown>
15:18:43  #7 0x564b7e3acd6b <unknown>
15:18:43  #8 0x564b7e3ae141 <unknown>
15:18:43  #9 0x564b7e8872ab <unknown>
15:18:43  #10 0x564b7e88b0b9 <unknown>
15:18:43  #11 0x564b7e86e139 <unknown>
15:18:43  #12 0x564b7e88bc68 <unknown>
15:18:43  #13 0x564b7e85260f <unknown>
15:18:43  #14 0x564b7e8b01f8 <unknown>
15:18:43  #15 0x564b7e8b03d6 <unknown>
15:18:43  #16 0x564b7e8c15e6 <unknown>
15:18:43  #17 0x7f4d7efcb609 start_thread
15:18:43  
15:18:43    - Tcs 02: Func-Course-04: Create course with empty required fields: Parent suite setup failed:
15:18:43  SessionNotCreatedException: Message: session not created: probably user data directory is already in use, please specify a unique value for --user-data-dir argument, or don't use --user-data-dir
15:18:43  Stacktrace:
15:18:43  #0 0x564b7e8c22ca <unknown>
15:18:43  #1 0x564b7e369550 <unknown>
15:18:43  #2 0x564b7e3a39cb <unknown>
15:18:43  #3 0x564b7e39ed17 <unknown>
15:18:43  #4 0x564b7e3ef19e <unknown>
15:18:43  #5 0x564b7e3ee766 <unknown>
15:18:43  #6 0x564b7e3e0993 <unknown>
15:18:43  #7 0x564b7e3acd6b <unknown>
15:18:43  #8 0x564b7e3ae141 <unknown>
15:18:43  #9 0x564b7e8872ab <unknown>
15:18:43  #10 0x564b7e88b0b9 <unknown>
15:18:43  #11 0x564b7e86e139 <unknown>
15:18:43  #12 0x564b7e88bc68 <unknown>
15:18:43  #13 0x564b7e85260f <unknown>
15:18:43  #14 0x564b7e8b01f8 <unknown>
15:18:43  #15 0x564b7e8b03d6 <unknown>
15:18:43  #16 0x564b7e8c15e6 <unknown>
15:18:43  #17 0x7f4d7efcb609 start_thread
15:18:43  
15:18:43    - Tcs 11: Func-Course: Create Course with maximum credits value: Parent suite setup failed:
15:18:43  SessionNotCreatedException: Message: session not created: probably user data directory is already in use, please specify a unique value for --user-data-dir argument, or don't use --user-data-dir
15:18:43  Stacktrace:
15:18:43  #0 0x564b7e8c22ca <unknown>
15:18:43  #1 0x564b7e369550 <unknown>
15:18:43  #2 0x564b7e3a39cb <unknown>
15:18:43  #3 0x564b7e39ed17 <unknown>
15:18:43  #4 0x564b7e3ef19e <unknown>
15:18:43  #5 0x564b7e3ee766 <unknown>
15:18:43  #6 0x564b7e3e0993 <unknown>
15:18:43  #7 0x564b7e3acd6b <unknown>
15:18:43  #8 0x564b7e3ae141 <unknown>
15:18:43  #9 0x564b7e8872ab <unknown>
15:18:43  #10 0x564b7e88b0b9 <unknown>
15:18:43  #11 0x564b7e86e139 <unknown>
15:18:43  #12 0x564b7e88bc68 <unknown>
15:18:43  #13 0x564b7e85260f <unknown>
15:18:43  #14 0x564b7e8b01f8 <unknown>
15:18:43  #15 0x564b7e8b03d6 <unknown>
15:18:43  #16 0x564b7e8c15e6 <unknown>
15:18:43  #17 0x7f4d7efcb609 start_thread
15:18:43  
15:18:43    - Tcs 03: Func-CLO-03: Create CLOs with fully fields: Parent suite setup failed:
15:18:43  SessionNotCreatedException: Message: session not created: probably user data directory is already in use, please specify a unique value for --user-data-dir argument, or don't use --user-data-dir
15:18:43  Stacktrace:
15:18:43  #0 0x564b7e8c22ca <unknown>
15:18:43  #1 0x564b7e369550 <unknown>
15:18:43  #2 0x564b7e3a39cb <unknown>
15:18:43  #3 0x564b7e39ed17 <unknown>
15:18:43  #4 0x564b7e3ef19e <unknown>
15:18:43  #5 0x564b7e3ee766 <unknown>
15:18:43  #6 0x564b7e3e0993 <unknown>
15:18:43  #7 0x564b7e3acd6b <unknown>
15:18:43  #8 0x564b7e3ae141 <unknown>
15:18:43  #9 0x564b7e8872ab <unknown>
15:18:43  #10 0x564b7e88b0b9 <unknown>
15:18:43  #11 0x564b7e86e139 <unknown>
15:18:43  #12 0x564b7e88bc68 <unknown>
15:18:43  #13 0x564b7e85260f <unknown>
15:18:43  #14 0x564b7e8b01f8 <unknown>
15:18:43  #15 0x564b7e8b03d6 <unknown>
15:18:43  #16 0x564b7e8c15e6 <unknown>
15:18:43  #17 0x7f4d7efcb609 start_thread
15:18:43  
15:18:43    - Tcs 04: Func-CLO-04: Create CLOS with empty required fields: Parent suite setup failed:
15:18:43  SessionNotCreatedException: Message: session not created: probably user data directory is already in use, please specify a unique value for --user-data-dir argument, or don't use --user-data-dir
15:18:43  Stacktrace:
15:18:43  #0 0x564b7e8c22ca <unknown>
15:18:43  #1 0x564b7e369550 <unknown>
15:18:43  #2 0x564b7e3a39cb <unknown>
15:18:43  #3 0x564b7e39ed17 <unknown>
15:18:43  #4 0x564b7e3ef19e <unknown>
15:18:43  #5 0x564b7e3ee766 <unknown>
15:18:43  #6 0x564b7e3e0993 <unknown>
15:18:43  #7 0x564b7e3acd6b <unknown>
15:18:43  #8 0x564b7e3ae141 <unknown>
15:18:43  #9 0x564b7e8872ab <unknown>
15:18:43  #10 0x564b7e88b0b9 <unknown>
15:18:43  #11 0x564b7e86e139 <unknown>
15:18:43  #12 0x564b7e88bc68 <unknown>
15:18:43  #13 0x564b7e85260f <unknown>
15:18:43  #14 0x564b7e8b01f8 <unknown>
15:18:43  #15 0x564b7e8b03d6 <unknown>
15:18:43  #16 0x564b7e8c15e6 <unknown>
15:18:43  #17 0x7f4d7efcb609 start_thread
15:18:43  
15:18:43    - Tcs 06: Edit Course: Parent suite setup failed:
15:18:43  SessionNotCreatedException: Message: session not created: probably user data directory is already in use, please specify a unique value for --user-data-dir argument, or don't use --user-data-dir
15:18:43  Stacktrace:
15:18:43  #0 0x564b7e8c22ca <unknown>
15:18:43  #1 0x564b7e369550 <unknown>
15:18:43  #2 0x564b7e3a39cb <unknown>
15:18:43  #3 0x564b7e39ed17 <unknown>
15:18:43  #4 0x564b7e3ef19e <unknown>
15:18:43  #5 0x564b7e3ee766 <unknown>
15:18:43  #6 0x564b7e3e0993 <unknown>
15:18:43  #7 0x564b7e3acd6b <unknown>
15:18:43  #8 0x564b7e3ae141 <unknown>
15:18:43  #9 0x564b7e8872ab <unknown>
15:18:43  #10 0x564b7e88b0b9 <unknown>
15:18:43  #11 0x564b7e86e139 <unknown>
15:18:43  #12 0x564b7e88bc68 <unknown>
15:18:43  #13 0x564b7e85260f <unknown>
15:18:43  #14 0x564b7e8b01f8 <unknown>
15:18:43  #15 0x564b7e8b03d6 <unknown>
15:18:43  #16 0x564b7e8c15e6 <unknown>
15:18:43  #17 0x7f4d7efcb609 start_thread
15:18:43  
15:18:43    - Tcs 08: Compare value CLO in course: Parent suite setup failed:
15:18:43  SessionNotCreatedException: Message: session not created: probably user data directory is already in use, please specify a unique value for --user-data-dir argument, or don't use --user-data-dir
15:18:43  Stacktrace:
15:18:43  #0 0x564b7e8c22ca <unknown>
15:18:43  #1 0x564b7e369550 <unknown>
15:18:43  #2 0x564b7e3a39cb <unknown>
15:18:43  #3 0x564b7e39ed17 <unknown>
15:18:43  #4 0x564b7e3ef19e <unknown>
15:18:43  #5 0x564b7e3ee766 <unknown>
15:18:43  #6 0x564b7e3e0993 <unknown>
15:18:43  #7 0x564b7e3acd6b <unknown>
15:18:43  #8 0x564b7e3ae141 <unknown>
15:18:43  #9 0x564b7e8872ab <unknown>
15:18:43  #10 0x564b7e88b0b9 <unknown>
15:18:43  #11 0x564b7e86e139 <unknown>
15:18:43  #12 0x564b7e88bc68 <unknown>
15:18:43  #13 0x564b7e85260f <unknown>
15:18:43  #14 0x564b7e8b01f8 <unknown>
15:18:43  #15 0x564b7e8b03d6 <unknown>
15:18:43  #16 0x564b7e8c15e6 <unknown>
15:18:43  #17 0x7f4d7efcb609 start_thread
15:18:43  
15:18:43    - TC 09: Edit All Fields In Course: Parent suite setup failed:
15:18:43  SessionNotCreatedException: Message: session not created: probably user data directory is already in use, please specify a unique value for --user-data-dir argument, or don't use --user-data-dir
15:18:43  Stacktrace:
15:18:43  #0 0x564b7e8c22ca <unknown>
15:18:43  #1 0x564b7e369550 <unknown>
15:18:43  #2 0x564b7e3a39cb <unknown>
15:18:43  #3 0x564b7e39ed17 <unknown>
15:18:43  #4 0x564b7e3ef19e <unknown>
15:18:43  #5 0x564b7e3ee766 <unknown>
15:18:43  #6 0x564b7e3e0993 <unknown>
15:18:43  #7 0x564b7e3acd6b <unknown>
15:18:43  #8 0x564b7e3ae141 <unknown>
15:18:43  #9 0x564b7e8872ab <unknown>
15:18:43  #10 0x564b7e88b0b9 <unknown>
15:18:43  #11 0x564b7e86e139 <unknown>
15:18:43  #12 0x564b7e88bc68 <unknown>
15:18:43  #13 0x564b7e85260f <unknown>
15:18:43  #14 0x564b7e8b01f8 <unknown>
15:18:43  #15 0x564b7e8b03d6 <unknown>
15:18:43  #16 0x564b7e8c15e6 <unknown>
15:18:43  #17 0x7f4d7efcb609 start_thread
15:18:43  
15:18:43    - Tcs 12: "Tạo mới" button inactive: Parent suite setup failed:
15:18:43  SessionNotCreatedException: Message: session not created: probably user data directory is already in use, please specify a unique value for --user-data-dir argument, or don't use --user-data-dir
15:18:43  Stacktrace:
15:18:43  #0 0x564b7e8c22ca <unknown>
15:18:43  #1 0x564b7e369550 <unknown>
15:18:43  #2 0x564b7e3a39cb <unknown>
15:18:43  #3 0x564b7e39ed17 <unknown>
15:18:43  #4 0x564b7e3ef19e <unknown>
15:18:43  #5 0x564b7e3ee766 <unknown>
15:18:43  #6 0x564b7e3e0993 <unknown>
15:18:43  #7 0x564b7e3acd6b <unknown>
15:18:43  #8 0x564b7e3ae141 <unknown>
15:18:43  #9 0x564b7e8872ab <unknown>
15:18:43  #10 0x564b7e88b0b9 <unknown>
15:18:43  #11 0x564b7e86e139 <unknown>
15:18:43  #12 0x564b7e88bc68 <unknown>
15:18:43  #13 0x564b7e85260f <unknown>
15:18:43  #14 0x564b7e8b01f8 <unknown>
15:18:43  #15 0x564b7e8b03d6 <unknown>
15:18:43  #16 0x564b7e8c15e6 <unknown>
15:18:43  #17 0x7f4d7efcb609 start_thread
15:18:43  
15:18:43    - Tc 13: Find word with valid and invalid Keyword: Parent suite setup failed:
15:18:43  SessionNotCreatedException: Message: session not created: probably user data directory is already in use, please specify a unique value for --user-data-dir argument, or don't use --user-data-dir
15:18:43  Stacktrace:
15:18:43  #0 0x564b7e8c22ca <unknown>
15:18:43  #1 0x564b7e369550 <unknown>
15:18:43  #2 0x564b7e3a39cb <unknown>
15:18:43  #3 0x564b7e39ed17 <unknown>
15:18:43  #4 0x564b7e3ef19e <unknown>
15:18:43  #5 0x564b7e3ee766 <unknown>
15:18:43  #6 0x564b7e3e0993 <unknown>
15:18:43  #7 0x564b7e3acd6b <unknown>
15:18:43  #8 0x564b7e3ae141 <unknown>
15:18:43  #9 0x564b7e8872ab <unknown>
15:18:43  #10 0x564b7e88b0b9 <unknown>
15:18:43  #11 0x564b7e86e139 <unknown>
15:18:43  #12 0x564b7e88bc68 <unknown>
15:18:43  #13 0x564b7e85260f <unknown>
15:18:43  #14 0x564b7e8b01f8 <unknown>
15:18:43  #15 0x564b7e8b03d6 <unknown>
15:18:43  #16 0x564b7e8c15e6 <unknown>
15:18:43  #17 0x7f4d7efcb609 start_thread
15:18:43  
15:18:43    - VERIFY FORM CREATE QUESTION IS DISPLAYED: Setup failed:
15:18:43  SessionNotCreatedException: Message: session not created: probably user data directory is already in use, please specify a unique value for --user-data-dir argument, or don't use --user-data-dir
15:18:43  Stacktrace:
15:18:43  #0 0x5654f810d2ca <unknown>
15:18:43  #1 0x5654f7bb4550 <unknown>
15:18:43  #2 0x5654f7bee9cb <unknown>
15:18:43  #3 0x5654f7be9d17 <unknown>
15:18:43  #4 0x5654f7c3a19e <unknown>
15:18:43  #5 0x5654f7c39766 <unknown>
15:18:43  #6 0x5654f7c2b993 <unknown>
15:18:43  #7 0x5654f7bf7d6b <unknown>
15:18:43  #8 0x5654f7bf9141 <unknown>
15:18:43  #9 0x5654f80d22ab <unknown>
15:18:43  #10 0x5654f80d60b9 <unknown>
15:18:43  #11 0x5654f80b9139 <unknown>
15:18:43  #12 0x5654f80d6c68 <unknown>
15:18:43  #13 0x5654f809d60f <unknown>
15:18:43  #14 0x5654f80fb1f8 <unknown>
15:18:43  #15 0x5654f80fb3d6 <unknown>
15:18:43  #16 0x5654f810c5e6 <unknown>
15:18:43  #17 0x7fdcbc69c609 start_thread
15:18:43  
15:18:43    - VERIFY DROPDOWN OPTION CREATE QUESTION FROM: Setup failed:
15:18:43  SessionNotCreatedException: Message: session not created: probably user data directory is already in use, please specify a unique value for --user-data-dir argument, or don't use --user-data-dir
15:18:43  Stacktrace:
15:18:43  #0 0x55b03439f2ca <unknown>
15:18:43  #1 0x55b033e46550 <unknown>
15:18:43  #2 0x55b033e809cb <unknown>
15:18:43  #3 0x55b033e7bd17 <unknown>
15:18:43  #4 0x55b033ecc19e <unknown>
15:18:43  #5 0x55b033ecb766 <unknown>
15:18:43  #6 0x55b033ebd993 <unknown>
15:18:43  #7 0x55b033e89d6b <unknown>
15:18:43  #8 0x55b033e8b141 <unknown>
15:18:43  #9 0x55b0343642ab <unknown>
15:18:43  #10 0x55b0343680b9 <unknown>
15:18:43  #11 0x55b03434b139 <unknown>
15:18:43  #12 0x55b034368c68 <unknown>
15:18:43  #13 0x55b03432f60f <unknown>
15:18:43  #14 0x55b03438d1f8 <unknown>
15:18:43  #15 0x55b03438d3d6 <unknown>
15:18:43  #16 0x55b03439e5e6 <unknown>
15:18:43  #17 0x7f3b621d7609 start_thread
15:18:43  
15:18:43    - VERIFY REQUIRED FIELD ERRORS: Setup failed:
15:18:43  SessionNotCreatedException: Message: session not created: probably user data directory is already in use, please specify a unique value for --user-data-dir argument, or don't use --user-data-dir
15:18:43  Stacktrace:
15:18:43  #0 0x560e447ac2ca <unknown>
15:18:43  #1 0x560e44253550 <unknown>
15:18:43  #2 0x560e4428d9cb <unknown>
15:18:43  #3 0x560e44288d17 <unknown>
15:18:43  #4 0x560e442d919e <unknown>
15:18:43  #5 0x560e442d8766 <unknown>
15:18:43  #6 0x560e442ca993 <unknown>
15:18:43  #7 0x560e44296d6b <unknown>
15:18:43  #8 0x560e44298141 <unknown>
15:18:43  #9 0x560e447712ab <unknown>
15:18:43  #10 0x560e447750b9 <unknown>
15:18:43  #11 0x560e44758139 <unknown>
15:18:43  #12 0x560e44775c68 <unknown>
15:18:43  #13 0x560e4473c60f <unknown>
15:18:43  #14 0x560e4479a1f8 <unknown>
15:18:43  #15 0x560e4479a3d6 <unknown>
15:18:43  #16 0x560e447ab5e6 <unknown>
15:18:43  #17 0x7f72678c5609 start_thread
15:18:43  
15:18:43    - VERIFY ANSWER FORM: Setup failed:
15:18:43  SessionNotCreatedException: Message: session not created: probably user data directory is already in use, please specify a unique value for --user-data-dir argument, or don't use --user-data-dir
15:18:43  Stacktrace:
15:18:43  #0 0x5637404232ca <unknown>
15:18:43  #1 0x56373feca550 <unknown>
15:18:43  #2 0x56373ff049cb <unknown>
15:18:43  #3 0x56373feffd17 <unknown>
15:18:43  #4 0x56373ff5019e <unknown>
15:18:43  #5 0x56373ff4f766 <unknown>
15:18:43  #6 0x56373ff41993 <unknown>
15:18:43  #7 0x56373ff0dd6b <unknown>
15:18:43  #8 0x56373ff0f141 <unknown>
15:18:43  #9 0x5637403e82ab <unknown>
15:18:43  #10 0x5637403ec0b9 <unknown>
15:18:43  #11 0x5637403cf139 <unknown>
15:18:43  #12 0x5637403ecc68 <unknown>
15:18:43  #13 0x5637403b360f <unknown>
15:18:43  #14 0x5637404111f8 <unknown>
15:18:43  #15 0x5637404113d6 <unknown>
15:18:43  #16 0x5637404225e6 <unknown>
15:18:43  #17 0x7efde7d16609 start_thread
15:18:43  
15:18:43    - CREATE SIMPLE QUESTION FORM: Setup failed:
15:18:43  SessionNotCreatedException: Message: session not created: probably user data directory is already in use, please specify a unique value for --user-data-dir argument, or don't use --user-data-dir
15:18:43  Stacktrace:
15:18:43  #0 0x55e55aeda2ca <unknown>
15:18:43  #1 0x55e55a981550 <unknown>
15:18:43  #2 0x55e55a9bb9cb <unknown>
15:18:43  #3 0x55e55a9b6d17 <unknown>
15:18:43  #4 0x55e55aa0719e <unknown>
15:18:43  #5 0x55e55aa06766 <unknown>
15:18:43  #6 0x55e55a9f8993 <unknown>
15:18:43  #7 0x55e55a9c4d6b <unknown>
15:18:43  #8 0x55e55a9c6141 <unknown>
15:18:43  #9 0x55e55ae9f2ab <unknown>
15:18:43  #10 0x55e55aea30b9 <unknown>
15:18:43  #11 0x55e55ae86139 <unknown>
15:18:43  #12 0x55e55aea3c68 <unknown>
15:18:43  #13 0x55e55ae6a60f <unknown>
15:18:43  #14 0x55e55aec81f8 <unknown>
15:18:43  #15 0x55e55aec83d6 <unknown>
15:18:43  #16 0x55e55aed95e6 <unknown>
15:18:43  #17 0x7f8b16253609 start_thread
15:18:43  
15:18:43    - Verify Login With User Admin: SessionNotCreatedException: Message: session not created: probably user data directory is already in use, please specify a unique value for --user-data-dir argument, or don't use --user-data-dir
15:18:43  Stacktrace:
15:18:43  #0 0x55764f2b42ca <unknown>
15:18:43  #1 0x55764ed5b550 <unknown>
15:18:43  #2 0x55764ed959cb <unknown>
15:18:43  #3 0x55764ed90d17 <unknown>
15:18:43  #4 0x55764ede119e <unknown>
15:18:43  #5 0x55764ede0766 <unknown>
15:18:43  #6 0x55764edd2993 <unknown>
15:18:43  #7 0x55764ed9ed6b <unknown>
15:18:43  #8 0x55764eda0141 <unknown>
15:18:43  #9 0x55764f2792ab <unknown>
15:18:43  #10 0x55764f27d0b9 <unknown>
15:18:43  #11 0x55764f260139 <unknown>
15:18:43  #12 0x55764f27dc68 <unknown>
15:18:43  #13 0x55764f24460f <unknown>
15:18:43  #14 0x55764f2a21f8 <unknown>
15:18:43  #15 0x55764f2a23d6 <unknown>
15:18:43  #16 0x55764f2b35e6 <unknown>
15:18:43  #17 0x7ff97b3e9609 start_thread
15:18:43  
15:18:43    - Verify Login With User Lecturer: SessionNotCreatedException: Message: session not created: probably user data directory is already in use, please specify a unique value for --user-data-dir argument, or don't use --user-data-dir
15:18:43  Stacktrace:
15:18:43  #0 0x562c4ac552ca <unknown>
15:18:43  #1 0x562c4a6fc550 <unknown>
15:18:43  #2 0x562c4a7369cb <unknown>
15:18:43  #3 0x562c4a731d17 <unknown>
15:18:43  #4 0x562c4a78219e <unknown>
15:18:43  #5 0x562c4a781766 <unknown>
15:18:43  #6 0x562c4a773993 <unknown>
15:18:43  #7 0x562c4a73fd6b <unknown>
15:18:43  #8 0x562c4a741141 <unknown>
15:18:43  #9 0x562c4ac1a2ab <unknown>
15:18:43  #10 0x562c4ac1e0b9 <unknown>
15:18:43  #11 0x562c4ac01139 <unknown>
15:18:43  #12 0x562c4ac1ec68 <unknown>
15:18:43  #13 0x562c4abe560f <unknown>
15:18:43  #14 0x562c4ac431f8 <unknown>
15:18:43  #15 0x562c4ac433d6 <unknown>
15:18:43  #16 0x562c4ac545e6 <unknown>
15:18:43  #17 0x7fb0a4cf3609 start_thread
15:18:43  
15:18:51    - Verify Login With Invalid User: SessionNotCreatedException: Message: session not created: probably user data directory is already in use, please specify a unique value for --user-data-dir argument, or don't use --user-data-dir
15:18:51  Stacktrace:
15:18:51  #0 0x55bc8a3f72ca <unknown>
15:18:51  #1 0x55bc89e9e550 <unknown>
15:18:51  #2 0x55bc89ed89cb <unknown>
15:18:51  #3 0x55bc89ed3d17 <unknown>
15:18:51  #4 0x55bc89f2419e <unknown>
15:18:51  #5 0x55bc89f23766 <unknown>
15:18:51  #6 0x55bc89f15993 <unknown>
15:18:51  #7 0x55bc89ee1d6b <unknown>
15:18:51  #8 0x55bc89ee3141 <unknown>
15:18:51  #9 0x55bc8a3bc2ab <unknown>
15:18:51  #10 0x55bc8a3c00b9 <unknown>
15:18:51  #11 0x55bc8a3a3139 <unknown>
15:18:51  #12 0x55bc8a3c0c68 <unknown>
15:18:51  #13 0x55bc8a38760f <unknown>
15:18:51  #14 0x55bc8a3e51f8 <unknown>
15:18:51  #15 0x55bc8a3e53d6 <unknown>
15:18:51  #16 0x55bc8a3f65e6 <unknown>
15:18:51  #17 0x7fd5258c2609 start_thread
15:18:51  
15:18:51    - Verify Login With Multiple Accounts From Excel: Variable '@{ROBOT_USERS_PANDAS}' not found.
15:18:51    - Verify successful create new program: SessionNotCreatedException: Message: session not created: probably user data directory is already in use, please specify a unique value for --user-data-dir argument, or don't use --user-data-dir
15:18:51  Stacktrace:
15:18:51  #0 0x56020a5342ca <unknown>
15:18:51  #1 0x560209fdb550 <unknown>
15:18:51  #2 0x56020a0159cb <unknown>
15:18:51  #3 0x56020a010d17 <unknown>
15:18:51  #4 0x56020a06119e <unknown>
15:18:51  #5 0x56020a060766 <unknown>
15:18:51  #6 0x56020a052993 <unknown>
15:18:51  #7 0x56020a01ed6b <unknown>
15:18:51  #8 0x56020a020141 <unknown>
15:18:51  #9 0x56020a4f92ab <unknown>
15:18:51  #10 0x56020a4fd0b9 <unknown>
15:18:51  #11 0x56020a4e0139 <unknown>
15:18:51  #12 0x56020a4fdc68 <unknown>
15:18:51  #13 0x56020a4c460f <unknown>
15:18:51  #14 0x56020a5221f8 <unknown>
15:18:51  #15 0x56020a5223d6 <unknown>
15:18:51  #16 0x56020a5335e6 <unknown>
15:18:51  #17 0x7f424704a609 start_thread
15:18:51  
15:18:51    - Verify create program fails when all fields are empty: SessionNotCreatedException: Message: session not created: probably user data directory is already in use, please specify a unique value for --user-data-dir argument, or don't use --user-data-dir
15:18:51  Stacktrace:
15:18:51  #0 0x5585339e42ca <unknown>
15:18:51  #1 0x55853348b550 <unknown>
15:18:51  #2 0x5585334c59cb <unknown>
15:18:51  #3 0x5585334c0d17 <unknown>
15:18:51  #4 0x55853351119e <unknown>
15:18:51  #5 0x558533510766 <unknown>
15:18:51  #6 0x558533502993 <unknown>
15:18:51  #7 0x5585334ced6b <unknown>
15:18:51  #8 0x5585334d0141 <unknown>
15:18:51  #9 0x5585339a92ab <unknown>
15:18:51  #10 0x5585339ad0b9 <unknown>
15:18:51  #11 0x558533990139 <unknown>
15:18:51  #12 0x5585339adc68 <unknown>
15:18:51  #13 0x55853397460f <unknown>
15:18:51  #14 0x5585339d21f8 <unknown>
15:18:51  #15 0x5585339d23d6 <unknown>
15:18:51  #16 0x5585339e35e6 <unknown>
15:18:51  #17 0x7f31e17bd609 start_thread
15:18:51  
15:18:51    - Verify go to Program page: SessionNotCreatedException: Message: session not created: probably user data directory is already in use, please specify a unique value for --user-data-dir argument, or don't use --user-data-dir
15:18:51  Stacktrace:
15:18:51  #0 0x561ed12e12ca <unknown>
15:18:51  #1 0x561ed0d88550 <unknown>
15:18:51  #2 0x561ed0dc29cb <unknown>
15:18:51  #3 0x561ed0dbdd17 <unknown>
15:18:51  #4 0x561ed0e0e19e <unknown>
15:18:51  #5 0x561ed0e0d766 <unknown>
15:18:51  #6 0x561ed0dff993 <unknown>
15:18:51  #7 0x561ed0dcbd6b <unknown>
15:18:51  #8 0x561ed0dcd141 <unknown>
15:18:51  #9 0x561ed12a62ab <unknown>
15:18:51  #10 0x561ed12aa0b9 <unknown>
15:18:51  #11 0x561ed128d139 <unknown>
15:18:51  #12 0x561ed12aac68 <unknown>
15:18:51  #13 0x561ed127160f <unknown>
15:18:51  #14 0x561ed12cf1f8 <unknown>
15:18:51  #15 0x561ed12cf3d6 <unknown>
15:18:51  #16 0x561ed12e05e6 <unknown>
15:18:51  #17 0x7f4f1821c609 start_thread
15:18:51  
15:18:51    - Search for program by Name: SessionNotCreatedException: Message: session not created: probably user data directory is already in use, please specify a unique value for --user-data-dir argument, or don't use --user-data-dir
15:18:51  Stacktrace:
15:18:51  #0 0x564c4b5712ca <unknown>
15:18:51  #1 0x564c4b018550 <unknown>
15:18:51  #2 0x564c4b0529cb <unknown>
15:18:51  #3 0x564c4b04dd17 <unknown>
15:18:51  #4 0x564c4b09e19e <unknown>
15:18:51  #5 0x564c4b09d766 <unknown>
15:18:51  #6 0x564c4b08f993 <unknown>
15:18:51  #7 0x564c4b05bd6b <unknown>
15:18:51  #8 0x564c4b05d141 <unknown>
15:18:51  #9 0x564c4b5362ab <unknown>
15:18:51  #10 0x564c4b53a0b9 <unknown>
15:18:51  #11 0x564c4b51d139 <unknown>
15:18:51  #12 0x564c4b53ac68 <unknown>
15:18:51  #13 0x564c4b50160f <unknown>
15:18:51  #14 0x564c4b55f1f8 <unknown>
15:18:51  #15 0x564c4b55f3d6 <unknown>
15:18:51  #16 0x564c4b5705e6 <unknown>
15:18:51  #17 0x7fb4f7708609 start_thread
15:18:51  
15:18:51    - Search for program not found: SessionNotCreatedException: Message: session not created: probably user data directory is already in use, please specify a unique value for --user-data-dir argument, or don't use --user-data-dir
15:18:51  Stacktrace:
15:18:51  #0 0x564c06d2f2ca <unknown>
15:18:51  #1 0x564c067d6550 <unknown>
15:18:51  #2 0x564c068109cb <unknown>
15:18:51  #3 0x564c0680bd17 <unknown>
15:18:51  #4 0x564c0685c19e <unknown>
15:18:51  #5 0x564c0685b766 <unknown>
15:18:51  #6 0x564c0684d993 <unknown>
15:18:51  #7 0x564c06819d6b <unknown>
15:18:51  #8 0x564c0681b141 <unknown>
15:18:51  #9 0x564c06cf42ab <unknown>
15:18:51  #10 0x564c06cf80b9 <unknown>
15:18:51  #11 0x564c06cdb139 <unknown>
15:18:51  #12 0x564c06cf8c68 <unknown>
15:18:51  #13 0x564c06cbf60f <unknown>
15:18:51  #14 0x564c06d1d1f8 <unknown>
15:18:51  #15 0x564c06d1d3d6 <unknown>
15:18:51  #16 0x564c06d2e5e6 <unknown>
15:18:51  #17 0x7f594c8d8609 start_thread
15:18:51  
15:18:51  Sending test results email...
15:18:51  Email sent <NAME_EMAIL>
15:18:51  ============================================================
15:18:51  TEST EXECUTION CYCLE COMPLETED
15:18:51  Tests executed: With errors
15:18:51  Email sent: Yes
15:18:51  ============================================================
15:18:51  
15:18:51  ========================================
15:18:51  FINAL SUMMARY
15:18:51  ========================================
15:18:51  Tests Run: 34
15:18:51  Passed: 0
15:18:51  Failed: 34
15:18:51  Success Rate: 0.00%
15:18:51  Overall Status: FAIL
15:18:51  Email Sent: Yes
15:18:51  + echo Tests completed with some failures
15:18:51  Tests completed with some failures
[Pipeline] }
[Pipeline] // stage
[Pipeline] stage
[Pipeline] { (Publish Results)
[Pipeline] echo
15:18:51  Publishing test results...
[Pipeline] archiveArtifacts
15:18:51  Archiving artifacts
[Pipeline] script
[Pipeline] {
[Pipeline] fileExists
[Pipeline] }
[Pipeline] // script
[Pipeline] }
[Pipeline] // stage
[Pipeline] stage
[Pipeline] { (Declarative: Post Actions)
[Pipeline] echo
15:18:52  Pipeline completed
[Pipeline] echo
15:18:52  Pipeline failed!
[Pipeline] }
[Pipeline] // stage
[Pipeline] }
[Pipeline] // timestamps
[Pipeline] }
[Pipeline] // timeout
[Pipeline] }
[Pipeline] // withEnv
[Pipeline] }
[Pipeline] // withEnv
[Pipeline] }
[Pipeline] // node
[Pipeline] End of Pipeline
Also:   org.jenkinsci.plugins.workflow.actions.ErrorAction$ErrorId: 34bbfd03-ed29-4c30-a085-e93bd4f6882b
java.lang.NoSuchMethodError: No such DSL method 'publishHTML' found among steps [archive, bat, build, catchError, checkout, deleteDir, dir, echo, emailext, emailextrecipients, envVarsForTool, error, fileExists, findBuildScans, getContext, git, input, isUnix, junit, library, libraryResource, load, mail, milestone, node, parallel, powershell, properties, publishChecks, pwd, pwsh, readFile, readTrusted, resolveScm, retry, script, sh, sleep, stage, stash, step, timeout, timestamps, tm, tool, unarchive, unstable, unstash, validateDeclarativePipeline, waitForBuild, waitUntil, warnError, withChecks, withContext, withCredentials, withEnv, withGradle, wrap, writeFile, ws] or symbols [GitUsernamePassword, agent, all, allBranchesSame, allOf, always, ant, antFromApache, antOutcome, antTarget, any, anyOf, apiToken, apiTokenProperty, architecture, archiveArtifacts, artifactManager, assembla, authorInChangelog, authorizationMatrix, batchFile, bitbucket, bitbucketServer, booleanParam, branch, brokenBuildSuspects, brokenTestsSuspects, browser, buildButton, buildDiscarder, buildDiscarders, buildRetention, buildSingleRevisionOnly, buildUser, buildingTag, builtInNode, caseInsensitive, caseSensitive, certificate, cgit, changeRequest, changelog, changelogBase, changelogToBranch, changeset, checkoutOption, checkoutToSubdirectory, choice, choiceParam, cleanAfterCheckout, cleanBeforeCheckout, cleanWs, clock, cloneOption, command, computerRetentionCheckInterval, consoleUrlProvider, contributor, cps, credentials, cron, crumb, culprits, dark, darkSystem, default, defaultDisplayUrlProvider, defaultFolderConfiguration, defaultView, demand, developers, disableConcurrentBuilds, disableRestartFromStage, disableResume, discoverOtherRefs, discoverOtherRefsTrait, diskSpace, diskSpaceMonitor, downstream, dumb, durabilityHint, email-ext, envVars, envVarsFilter, environment, equals, experimentalFlags, expression, extendedEmailPublisher, file, fileParam, filePath, fingerprint, fingerprints, firstBuildChangelog, fisheye, frameOptions, freeStyle, freeStyleJob, fromScm, fromSource, git, gitBranchDiscovery, gitHooks, gitHubBranchDiscovery, gitHubBranchHeadAuthority, gitHubExcludeArchivedRepositories, gitHubExcludeForkedRepositories, gitHubExcludePrivateRepositories, gitHubExcludePublicRepositories, gitHubForkDiscovery, gitHubIgnoreDraftPullRequestFilter, gitHubPullRequestDiscovery, gitHubSshCheckout, gitHubTagDiscovery, gitHubTopicsFilter, gitHubTrustContributors, gitHubTrustEveryone, gitHubTrustNobody, gitHubTrustPermissions, gitLab, gitList, gitSCM, gitTagDiscovery, gitTool, gitUsernamePassword, gitWeb, gitblit, github, githubProjectProperty, githubPush, gitiles, gogs, gradle, group, headRegexFilter, headWildcardFilter, hyperlink, hyperlinkToModels, ignoreOnPush, inbound, inheriting, inheritingGlobal, installSource, isRestartedRun, jdk, jgit, jgitapache, jnlp, jobBuildDiscarder, jobName, junitTestResultStorage, kiln, label, lastDuration, lastFailure, lastGrantedAuthorities, lastStable, lastSuccess, legacy, legacySCM, lfs, list, local, localBranch, localBranchTrait, location, logRotator, loggedInUsersCanDoAnything, mailer, masterBuild, maven, maven3Mojos, mavenErrors, mavenGlobalConfig, mavenMojos, mavenWarnings, modernSCM, multiBranchProjectDisplayNaming, multibranch, myView, namedBranchesDifferent, node, nodeProperties, nonInheriting, none, nonresumable, not, organizationFolder, overrideIndexTriggers, paneStatus, parallelsAlwaysFailFast, parameters, password, pattern, perBuildTag, permanent, phabricator, pipeline, pipelineGraphView, pipelineTriggers, plainText, plugin, pollSCM, preserveStashes, previous, projectNamingStrategy, proxy, pruneStaleBranch, pruneStaleTag, pruneTags, queueItemAuthenticator, quietPeriod, rateLimit, rateLimitBuilds, recipients, redmine, refSpecs, remoteName, requestor, resourceRoot, responseTime, retainOnlyVariables, rhodeCode, run, runParam, sSHLauncher, schedule, scmGit, scmRetryCount, scriptApproval, scriptApprovalLink, search, security, shell, simpleBuildDiscarder, skipDefaultCheckout, skipStagesAfterUnstable, slave, sourceRegexFilter, sourceWildcardFilter, sparseCheckout, sparseCheckoutPaths, ssh, sshUserPrivateKey, standard, status, string, stringParam, submodule, submoduleOption, suppressAutomaticTriggering, suppressFolderAutomaticTriggering, swapSpace, tag, teamFoundation, teamSlugFilter, text, textParam, themeManager, timestamper, timestamperConfig, timezone, tmpSpace, toolLocation, triggeredBy, unsecured, untrusted, upstream, upstreamDevelopers, user, userIdentity, userOrGroup, userSeed, usernameColonPassword, usernamePassword, viewgit, viewsTabBar, weather, withAnt, zip] or globals [currentBuild, env, params, pipeline, scm]
	at PluginClassLoader for workflow-cps//org.jenkinsci.plugins.workflow.cps.DSL.invokeMethod(DSL.java:219)
	at PluginClassLoader for workflow-cps//org.jenkinsci.plugins.workflow.cps.CpsScript.invokeMethod(CpsScript.java:124)
	at jdk.internal.reflect.GeneratedMethodAccessor4214.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.codehaus.groovy.reflection.CachedMethod.invoke(CachedMethod.java:98)
	at groovy.lang.MetaMethod.doMethodInvoke(MetaMethod.java:325)
	at groovy.lang.MetaClassImpl.invokeMethod(MetaClassImpl.java:1225)
	at groovy.lang.MetaClassImpl.invokeMethod(MetaClassImpl.java:1034)
	at org.codehaus.groovy.runtime.callsite.PogoMetaClassSite.call(PogoMetaClassSite.java:41)
	at org.codehaus.groovy.runtime.callsite.CallSiteArray.defaultCall(CallSiteArray.java:47)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.call(AbstractCallSite.java:116)
	at PluginClassLoader for script-security//org.kohsuke.groovy.sandbox.impl.Checker$1.call(Checker.java:180)
	at PluginClassLoader for script-security//org.kohsuke.groovy.sandbox.GroovyInterceptor.onMethodCall(GroovyInterceptor.java:23)
	at PluginClassLoader for script-security//org.jenkinsci.plugins.scriptsecurity.sandbox.groovy.SandboxInterceptor.onMethodCall(SandboxInterceptor.java:163)
	at PluginClassLoader for script-security//org.kohsuke.groovy.sandbox.impl.Checker$1.call(Checker.java:178)
	at PluginClassLoader for script-security//org.kohsuke.groovy.sandbox.impl.Checker.checkedCall(Checker.java:182)
	at PluginClassLoader for script-security//org.kohsuke.groovy.sandbox.impl.Checker.checkedCall(Checker.java:152)
	at PluginClassLoader for script-security//org.kohsuke.groovy.sandbox.impl.Checker.checkedCall(Checker.java:152)
	at PluginClassLoader for workflow-cps//com.cloudbees.groovy.cps.sandbox.SandboxInvoker.methodCall(SandboxInvoker.java:17)
	at PluginClassLoader for workflow-cps//org.jenkinsci.plugins.workflow.cps.LoggingInvoker.methodCall(LoggingInvoker.java:117)
	at WorkflowScript.run(WorkflowScript:51)
	at ___cps.transform___(Native Method)
	at PluginClassLoader for workflow-cps//com.cloudbees.groovy.cps.impl.ContinuationGroup.methodCall(ContinuationGroup.java:90)
	at PluginClassLoader for workflow-cps//com.cloudbees.groovy.cps.impl.FunctionCallBlock$ContinuationImpl.dispatchOrArg(FunctionCallBlock.java:116)
	at PluginClassLoader for workflow-cps//com.cloudbees.groovy.cps.impl.FunctionCallBlock$ContinuationImpl.fixArg(FunctionCallBlock.java:85)
	at jdk.internal.reflect.GeneratedMethodAccessor3120.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at PluginClassLoader for workflow-cps//com.cloudbees.groovy.cps.impl.ContinuationPtr$ContinuationImpl.receive(ContinuationPtr.java:72)
	at PluginClassLoader for workflow-cps//com.cloudbees.groovy.cps.impl.CollectionLiteralBlock$ContinuationImpl.dispatch(CollectionLiteralBlock.java:55)
	at PluginClassLoader for workflow-cps//com.cloudbees.groovy.cps.impl.CollectionLiteralBlock$ContinuationImpl.item(CollectionLiteralBlock.java:45)
	at jdk.internal.reflect.GeneratedMethodAccessor3221.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at PluginClassLoader for workflow-cps//com.cloudbees.groovy.cps.impl.ContinuationPtr$ContinuationImpl.receive(ContinuationPtr.java:72)
	at PluginClassLoader for workflow-cps//com.cloudbees.groovy.cps.impl.ConstantBlock.eval(ConstantBlock.java:21)
	at PluginClassLoader for workflow-cps//com.cloudbees.groovy.cps.Next.step(Next.java:83)
	at PluginClassLoader for workflow-cps//com.cloudbees.groovy.cps.Continuable.run0(Continuable.java:147)
	at PluginClassLoader for workflow-cps//org.jenkinsci.plugins.workflow.cps.SandboxContinuable.access$001(SandboxContinuable.java:17)
	at PluginClassLoader for workflow-cps//org.jenkinsci.plugins.workflow.cps.SandboxContinuable.run0(SandboxContinuable.java:49)
	at PluginClassLoader for workflow-cps//org.jenkinsci.plugins.workflow.cps.CpsThread.runNextChunk(CpsThread.java:180)
	at PluginClassLoader for workflow-cps//org.jenkinsci.plugins.workflow.cps.CpsThreadGroup.run(CpsThreadGroup.java:422)
	at PluginClassLoader for workflow-cps//org.jenkinsci.plugins.workflow.cps.CpsThreadGroup$2.call(CpsThreadGroup.java:330)
	at PluginClassLoader for workflow-cps//org.jenkinsci.plugins.workflow.cps.CpsThreadGroup$2.call(CpsThreadGroup.java:294)
	at PluginClassLoader for workflow-cps//org.jenkinsci.plugins.workflow.cps.CpsVmExecutorService.lambda$wrap$4(CpsVmExecutorService.java:140)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at hudson.remoting.SingleLaneExecutorService$1.run(SingleLaneExecutorService.java:139)
	at jenkins.util.ContextResettingExecutorService$1.run(ContextResettingExecutorService.java:28)
	at jenkins.security.ImpersonatingExecutorService$1.run(ImpersonatingExecutorService.java:68)
	at jenkins.util.ErrorLoggingExecutorService.lambda$wrap$0(ErrorLoggingExecutorService.java:51)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at PluginClassLoader for workflow-cps//org.jenkinsci.plugins.workflow.cps.CpsVmExecutorService$1.call(CpsVmExecutorService.java:53)
	at PluginClassLoader for workflow-cps//org.jenkinsci.plugins.workflow.cps.CpsVmExecutorService$1.call(CpsVmExecutorService.java:50)
	at org.codehaus.groovy.runtime.GroovyCategorySupport$ThreadCategoryInfo.use(GroovyCategorySupport.java:136)
	at org.codehaus.groovy.runtime.GroovyCategorySupport.use(GroovyCategorySupport.java:275)
	at PluginClassLoader for workflow-cps//org.jenkinsci.plugins.workflow.cps.CpsVmExecutorService.lambda$categoryThreadFactory$0(CpsVmExecutorService.java:50)
	at java.base/java.lang.Thread.run(Thread.java:840)
Finished: FAILURE
