Started by user <PERSON><PERSON> Tran
Obtained <PERSON><PERSON><PERSON> from git https://github.com/rainscales-qc-automation/clone-clo-auto.git
[Pipeline] Start of Pipeline
[Pipeline] node
Running on Jenkins in /var/lib/jenkins/workspace/v-job
[Pipeline] {
[Pipeline] stage
[Pipeline] { (Declarative: Checkout SCM)
[Pipeline] checkout
The recommended git tool is: git
using credential 348a7930-21a5-4eb7-bad5-9b230cce1689
 > git rev-parse --resolve-git-dir /var/lib/jenkins/workspace/v-job/.git # timeout=10
Fetching changes from the remote Git repository
 > git config remote.origin.url https://github.com/rainscales-qc-automation/clone-clo-auto.git # timeout=10
Fetching upstream changes from https://github.com/rainscales-qc-automation/clone-clo-auto.git
 > git --version # timeout=10
 > git --version # 'git version 2.25.1'
using GIT_ASKPASS to set credentials 
 > git fetch --tags --force --progress -- https://github.com/rainscales-qc-automation/clone-clo-auto.git +refs/heads/*:refs/remotes/origin/* # timeout=10
 > git rev-parse refs/remotes/origin/main^{commit} # timeout=10
Checking out Revision 5c0cee0d1f1abc164e83befeebbc85d9569650ea (refs/remotes/origin/main)
 > git config core.sparsecheckout # timeout=10
 > git checkout -f 5c0cee0d1f1abc164e83befeebbc85d9569650ea # timeout=10
Commit message: "jenkin no docker"
First time build. Skipping changelog.
[Pipeline] }
[Pipeline] // stage
[Pipeline] withEnv
[Pipeline] {
[Pipeline] withEnv
[Pipeline] {
[Pipeline] timeout
Timeout set to expire in 30 min
[Pipeline] {
[Pipeline] timestamps
[Pipeline] {
[Pipeline] stage
[Pipeline] { (Checkout)
[Pipeline] echo
[2025-07-31T07:47:38.674Z] Checking out source code...
[Pipeline] checkout
[2025-07-31T07:47:38.722Z] The recommended git tool is: git
[2025-07-31T07:47:38.723Z] using credential 348a7930-21a5-4eb7-bad5-9b230cce1689
[2025-07-31T07:47:38.738Z]  > git rev-parse --resolve-git-dir /var/lib/jenkins/workspace/v-job/.git # timeout=10
[2025-07-31T07:47:38.781Z] Fetching changes from the remote Git repository
[2025-07-31T07:47:38.787Z]  > git config remote.origin.url https://github.com/rainscales-qc-automation/clone-clo-auto.git # timeout=10
[2025-07-31T07:47:38.796Z] Fetching upstream changes from https://github.com/rainscales-qc-automation/clone-clo-auto.git
[2025-07-31T07:47:38.796Z]  > git --version # timeout=10
[2025-07-31T07:47:38.802Z]  > git --version # 'git version 2.25.1'
[2025-07-31T07:47:38.802Z] using GIT_ASKPASS to set credentials 
[2025-07-31T07:47:38.812Z]  > git fetch --tags --force --progress -- https://github.com/rainscales-qc-automation/clone-clo-auto.git +refs/heads/*:refs/remotes/origin/* # timeout=10
[2025-07-31T07:47:39.423Z]  > git rev-parse refs/remotes/origin/main^{commit} # timeout=10
[2025-07-31T07:47:39.430Z] Checking out Revision 5c0cee0d1f1abc164e83befeebbc85d9569650ea (refs/remotes/origin/main)
[2025-07-31T07:47:39.430Z]  > git config core.sparsecheckout # timeout=10
[2025-07-31T07:47:39.435Z]  > git checkout -f 5c0cee0d1f1abc164e83befeebbc85d9569650ea # timeout=10
[2025-07-31T07:47:39.448Z] Commit message: "jenkin no docker"
[Pipeline] sh
[2025-07-31T07:47:39.759Z] + echo Workspace: /var/lib/jenkins/workspace/v-job
[2025-07-31T07:47:39.759Z] Workspace: /var/lib/jenkins/workspace/v-job
[2025-07-31T07:47:39.760Z] + echo Build Number: 5
[2025-07-31T07:47:39.760Z] Build Number: 5
[2025-07-31T07:47:39.760Z] + echo Branch: origin/main
[2025-07-31T07:47:39.760Z] Branch: origin/main
[2025-07-31T07:47:39.760Z] + ls -la
[2025-07-31T07:47:39.760Z] total 64
[2025-07-31T07:47:39.760Z] drwxr-xr-x  9 <USER> <GROUP> 4096 Jul 31 14:47 .
[2025-07-31T07:47:39.760Z] drwxr-xr-x 17 <USER> <GROUP> 4096 Jul 31 14:15 ..
[2025-07-31T07:47:39.760Z] -rw-r--r--  1 <USER> <GROUP> 3858 Jul 31 14:37 #2.txt
[2025-07-31T07:47:39.760Z] drwxr-xr-x  2 <USER> <GROUP> 4096 Jul 31 14:47 deploy
[2025-07-31T07:47:39.760Z] drwxr-xr-x  8 <USER> <GROUP> 4096 Jul 31 14:47 .git
[2025-07-31T07:47:39.760Z] -rw-r--r--  1 <USER> <GROUP>  531 Jul 31 14:15 .gitignore
[2025-07-31T07:47:39.760Z] -rw-r--r--  1 <USER> <GROUP> 6232 Jul 31 14:47 Jenkinsfile
[2025-07-31T07:47:39.760Z] drwxr-xr-x  2 <USER> <GROUP> 4096 Jul 31 14:15 libs
[2025-07-31T07:47:39.760Z] drwxr-xr-x  6 <USER> <GROUP> 4096 Jul 31 14:15 pages
[2025-07-31T07:47:39.760Z] -rw-r--r--  1 <USER> <GROUP> 1930 Jul 31 14:15 Readme.md
[2025-07-31T07:47:39.760Z] -rw-r--r--  1 <USER> <GROUP>  104 Jul 31 14:15 requirements.txt
[2025-07-31T07:47:39.760Z] drwxr-xr-x  2 <USER> <GROUP> 4096 Jul 31 14:15 resources
[2025-07-31T07:47:39.760Z] -rw-r--r--  1 <USER> <GROUP> 1485 Jul 31 14:15 run_tests.py
[2025-07-31T07:47:39.760Z] drwxr-xr-x  2 <USER> <GROUP> 4096 Jul 31 14:15 test
[2025-07-31T07:47:39.760Z] drwxr-xr-x  2 <USER> <GROUP> 4096 Jul 31 14:15 test_runner
[Pipeline] }
[Pipeline] // stage
[Pipeline] stage
[Pipeline] { (Build Docker Image)
[Pipeline] echo
[2025-07-31T07:47:39.956Z] Building Docker image...
[Pipeline] sh
[2025-07-31T07:47:40.244Z] + 
[2025-07-31T07:47:40.244Z] + echo Docker is not installed or not in PATH
[2025-07-31T07:47:40.244Z] Docker is not installed or not in PATH
[2025-07-31T07:47:40.244Z] + exit 1
[2025-07-31T07:47:40.244Z] + command -v docker
[2025-07-31T07:47:40.244Z] /usr/bin/docker
[Pipeline] }
[Pipeline] // stage
[Pipeline] stage
[Pipeline] { (Run Tests)
Stage "Run Tests" skipped due to earlier failure(s)
[Pipeline] getContext
[Pipeline] }
[Pipeline] // stage
[Pipeline] stage
[Pipeline] { (Publish Results)
Stage "Publish Results" skipped due to earlier failure(s)
[Pipeline] getContext
[Pipeline] }
[Pipeline] // stage
[Pipeline] stage
[Pipeline] { (Declarative: Post Actions)
[Pipeline] echo
[2025-07-31T07:47:40.788Z] Pipeline completed
[Pipeline] script
[Pipeline] {
[Pipeline] sh
[2025-07-31T07:47:41.140Z] + docker info
[2025-07-31T07:47:42.490Z] + echo Docker not accessible, skipping cleanup
[2025-07-31T07:47:42.491Z] Docker not accessible, skipping cleanup
[Pipeline] }
[Pipeline] // script
[Pipeline] echo
[2025-07-31T07:47:42.623Z] Pipeline failed!
[Pipeline] }
[Pipeline] // stage
[Pipeline] }
[Pipeline] // timestamps
[Pipeline] }
[Pipeline] // timeout
[Pipeline] }
[Pipeline] // withEnv
[Pipeline] }
[Pipeline] // withEnv
[Pipeline] }
[Pipeline] // node
[Pipeline] End of Pipeline
ERROR: script returned exit code 1
Finished: FAILURE
