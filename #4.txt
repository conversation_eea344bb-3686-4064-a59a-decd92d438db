Started by user <PERSON><PERSON> Tran
Obtained <PERSON><PERSON><PERSON> from git https://github.com/rainscales-qc-automation/clone-clo-auto.git
[Pipeline] Start of Pipeline
[Pipeline] node
Running on Jenkins in /var/lib/jenkins/workspace/v-job
[Pipeline] {
[Pipeline] stage
[Pipeline] { (Declarative: Checkout SCM)
[Pipeline] checkout
The recommended git tool is: git
using credential 348a7930-21a5-4eb7-bad5-9b230cce1689
 > git rev-parse --resolve-git-dir /var/lib/jenkins/workspace/v-job/.git # timeout=10
Fetching changes from the remote Git repository
 > git config remote.origin.url https://github.com/rainscales-qc-automation/clone-clo-auto.git # timeout=10
Fetching upstream changes from https://github.com/rainscales-qc-automation/clone-clo-auto.git
 > git --version # timeout=10
 > git --version # 'git version 2.25.1'
using GIT_ASKPASS to set credentials 
 > git fetch --tags --force --progress -- https://github.com/rainscales-qc-automation/clone-clo-auto.git +refs/heads/*:refs/remotes/origin/* # timeout=10
 > git rev-parse refs/remotes/origin/main^{commit} # timeout=10
Checking out Revision 5764d52f2cb589f8f1e7103e4417a3b4b914911d (refs/remotes/origin/main)
 > git config core.sparsecheckout # timeout=10
 > git checkout -f 5764d52f2cb589f8f1e7103e4417a3b4b914911d # timeout=10
Commit message: "update"
First time build. Skipping changelog.
[Pipeline] }
[Pipeline] // stage
[Pipeline] withEnv
[Pipeline] {
[Pipeline] withEnv
[Pipeline] {
[Pipeline] timeout
Timeout set to expire in 30 min
[Pipeline] {
[Pipeline] timestamps
[Pipeline] {
[Pipeline] stage
[Pipeline] { (Checkout)
[Pipeline] echo
[2025-07-31T07:37:53.098Z] Checking out source code...
[Pipeline] checkout
[2025-07-31T07:37:53.126Z] The recommended git tool is: git
[2025-07-31T07:37:53.127Z] using credential 348a7930-21a5-4eb7-bad5-9b230cce1689
[2025-07-31T07:37:53.135Z]  > git rev-parse --resolve-git-dir /var/lib/jenkins/workspace/v-job/.git # timeout=10
[2025-07-31T07:37:53.145Z] Fetching changes from the remote Git repository
[2025-07-31T07:37:53.150Z]  > git config remote.origin.url https://github.com/rainscales-qc-automation/clone-clo-auto.git # timeout=10
[2025-07-31T07:37:53.157Z] Fetching upstream changes from https://github.com/rainscales-qc-automation/clone-clo-auto.git
[2025-07-31T07:37:53.157Z]  > git --version # timeout=10
[2025-07-31T07:37:53.175Z]  > git --version # 'git version 2.25.1'
[2025-07-31T07:37:53.175Z] using GIT_ASKPASS to set credentials 
[2025-07-31T07:37:53.182Z]  > git fetch --tags --force --progress -- https://github.com/rainscales-qc-automation/clone-clo-auto.git +refs/heads/*:refs/remotes/origin/* # timeout=10
[2025-07-31T07:37:53.771Z]  > git rev-parse refs/remotes/origin/main^{commit} # timeout=10
[2025-07-31T07:37:53.777Z] Checking out Revision 5764d52f2cb589f8f1e7103e4417a3b4b914911d (refs/remotes/origin/main)
[2025-07-31T07:37:53.777Z]  > git config core.sparsecheckout # timeout=10
[2025-07-31T07:37:53.783Z]  > git checkout -f 5764d52f2cb589f8f1e7103e4417a3b4b914911d # timeout=10
[2025-07-31T07:37:53.797Z] Commit message: "update"
[Pipeline] sh
[2025-07-31T07:37:54.089Z] + echo Workspace: /var/lib/jenkins/workspace/v-job
[2025-07-31T07:37:54.089Z] Workspace: /var/lib/jenkins/workspace/v-job
[2025-07-31T07:37:54.089Z] + echo Build Number: 4
[2025-07-31T07:37:54.089Z] Build Number: 4
[2025-07-31T07:37:54.089Z] + echo Branch: origin/main
[2025-07-31T07:37:54.089Z] Branch: origin/main
[2025-07-31T07:37:54.089Z] + ls -la
[2025-07-31T07:37:54.089Z] total 64
[2025-07-31T07:37:54.089Z] drwxr-xr-x  9 <USER> <GROUP> 4096 Jul 31 14:37 .
[2025-07-31T07:37:54.089Z] drwxr-xr-x 17 <USER> <GROUP> 4096 Jul 31 14:15 ..
[2025-07-31T07:37:54.089Z] -rw-r--r--  1 <USER> <GROUP> 3858 Jul 31 14:37 #2.txt
[2025-07-31T07:37:54.089Z] drwxr-xr-x  2 <USER> <GROUP> 4096 Jul 31 14:37 deploy
[2025-07-31T07:37:54.089Z] drwxr-xr-x  8 <USER> <GROUP> 4096 Jul 31 14:37 .git
[2025-07-31T07:37:54.089Z] -rw-r--r--  1 <USER> <GROUP>  531 Jul 31 14:15 .gitignore
[2025-07-31T07:37:54.089Z] -rw-r--r--  1 <USER> <GROUP> 5282 Jul 31 14:37 Jenkinsfile
[2025-07-31T07:37:54.089Z] drwxr-xr-x  2 <USER> <GROUP> 4096 Jul 31 14:15 libs
[2025-07-31T07:37:54.089Z] drwxr-xr-x  6 <USER> <GROUP> 4096 Jul 31 14:15 pages
[2025-07-31T07:37:54.089Z] -rw-r--r--  1 <USER> <GROUP> 1930 Jul 31 14:15 Readme.md
[2025-07-31T07:37:54.089Z] -rw-r--r--  1 <USER> <GROUP>  104 Jul 31 14:15 requirements.txt
[2025-07-31T07:37:54.089Z] drwxr-xr-x  2 <USER> <GROUP> 4096 Jul 31 14:15 resources
[2025-07-31T07:37:54.089Z] -rw-r--r--  1 <USER> <GROUP> 1485 Jul 31 14:15 run_tests.py
[2025-07-31T07:37:54.089Z] drwxr-xr-x  2 <USER> <GROUP> 4096 Jul 31 14:15 test
[2025-07-31T07:37:54.089Z] drwxr-xr-x  2 <USER> <GROUP> 4096 Jul 31 14:15 test_runner
[Pipeline] }
[Pipeline] // stage
[Pipeline] stage
[Pipeline] { (Build Docker Image)
[Pipeline] echo
[2025-07-31T07:37:54.284Z] Building Docker image...
[Pipeline] script
[Pipeline] {
[Pipeline] }
[Pipeline] // script
[Pipeline] }
[Pipeline] // stage
[Pipeline] stage
[Pipeline] { (Run Tests)
Stage "Run Tests" skipped due to earlier failure(s)
[Pipeline] getContext
[Pipeline] }
[Pipeline] // stage
[Pipeline] stage
[Pipeline] { (Publish Results)
Stage "Publish Results" skipped due to earlier failure(s)
[Pipeline] getContext
[Pipeline] }
[Pipeline] // stage
[Pipeline] stage
[Pipeline] { (Declarative: Post Actions)
[Pipeline] echo
[2025-07-31T07:37:55.186Z] Pipeline completed
[Pipeline] sh
[2025-07-31T07:37:55.480Z] + awk {print $1}
[2025-07-31T07:37:55.480Z] + docker ps -a
[2025-07-31T07:37:55.480Z] + grep robot-tests-4
[2025-07-31T07:37:55.480Z] + xargs -r docker rm -f
[2025-07-31T07:37:55.731Z] permission denied while trying to connect to the Docker daemon socket at unix:///var/run/docker.sock: Get "http://%2Fvar%2Frun%2Fdocker.sock/v1.47/containers/json?all=1": dial unix /var/run/docker.sock: connect: permission denied
[2025-07-31T07:37:55.731Z] + tail -n +2
[2025-07-31T07:37:55.731Z] + docker images robot-framework-tests --format table {{.Tag}}
[2025-07-31T07:37:55.731Z] + sort -nr
[2025-07-31T07:37:55.732Z] + tail -n +6
[2025-07-31T07:37:55.732Z] + xargs -r -I {} docker rmi robot-framework-tests:{}
[2025-07-31T07:37:55.982Z] permission denied while trying to connect to the Docker daemon socket at unix:///var/run/docker.sock: Head "http://%2Fvar%2Frun%2Fdocker.sock/_ping": dial unix /var/run/docker.sock: connect: permission denied
[Pipeline] echo
[2025-07-31T07:37:56.039Z] Pipeline failed!
[Pipeline] }
[Pipeline] // stage
[Pipeline] }
[Pipeline] // timestamps
[Pipeline] }
[Pipeline] // timeout
[Pipeline] }
[Pipeline] // withEnv
[Pipeline] }
[Pipeline] // withEnv
[Pipeline] }
[Pipeline] // node
[Pipeline] End of Pipeline
Also:   org.jenkinsci.plugins.workflow.actions.ErrorAction$ErrorId: 43e3a04d-a6b9-4b58-9174-b776d3263750
groovy.lang.MissingPropertyException: No such property: docker for class: groovy.lang.Binding
	at groovy.lang.Binding.getVariable(Binding.java:63)
	at PluginClassLoader for script-security//org.jenkinsci.plugins.scriptsecurity.sandbox.groovy.SandboxInterceptor.onGetProperty(SandboxInterceptor.java:285)
	at PluginClassLoader for script-security//org.kohsuke.groovy.sandbox.impl.Checker$7.call(Checker.java:375)
	at PluginClassLoader for script-security//org.kohsuke.groovy.sandbox.impl.Checker.checkedGetProperty(Checker.java:379)
	at PluginClassLoader for script-security//org.kohsuke.groovy.sandbox.impl.Checker.checkedGetProperty(Checker.java:355)
	at PluginClassLoader for script-security//org.kohsuke.groovy.sandbox.impl.Checker.checkedGetProperty(Checker.java:355)
	at PluginClassLoader for script-security//org.kohsuke.groovy.sandbox.impl.Checker.checkedGetProperty(Checker.java:355)
	at PluginClassLoader for workflow-cps//com.cloudbees.groovy.cps.sandbox.SandboxInvoker.getProperty(SandboxInvoker.java:29)
	at PluginClassLoader for workflow-cps//org.jenkinsci.plugins.workflow.cps.LoggingInvoker.getProperty(LoggingInvoker.java:133)
	at PluginClassLoader for workflow-cps//com.cloudbees.groovy.cps.impl.PropertyAccessBlock.rawGet(PropertyAccessBlock.java:20)
	at WorkflowScript.run(WorkflowScript:48)
	at ___cps.transform___(Native Method)
	at PluginClassLoader for workflow-cps//com.cloudbees.groovy.cps.impl.PropertyishBlock$ContinuationImpl.get(PropertyishBlock.java:73)
	at PluginClassLoader for workflow-cps//com.cloudbees.groovy.cps.LValueBlock$GetAdapter.receive(LValueBlock.java:30)
	at PluginClassLoader for workflow-cps//com.cloudbees.groovy.cps.impl.PropertyishBlock$ContinuationImpl.fixName(PropertyishBlock.java:65)
	at jdk.internal.reflect.GeneratedMethodAccessor3122.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at PluginClassLoader for workflow-cps//com.cloudbees.groovy.cps.impl.ContinuationPtr$ContinuationImpl.receive(ContinuationPtr.java:72)
	at PluginClassLoader for workflow-cps//com.cloudbees.groovy.cps.impl.ConstantBlock.eval(ConstantBlock.java:21)
	at PluginClassLoader for workflow-cps//com.cloudbees.groovy.cps.Next.step(Next.java:83)
	at PluginClassLoader for workflow-cps//com.cloudbees.groovy.cps.Continuable.run0(Continuable.java:147)
	at PluginClassLoader for workflow-cps//org.jenkinsci.plugins.workflow.cps.SandboxContinuable.access$001(SandboxContinuable.java:17)
	at PluginClassLoader for workflow-cps//org.jenkinsci.plugins.workflow.cps.SandboxContinuable.run0(SandboxContinuable.java:49)
	at PluginClassLoader for workflow-cps//org.jenkinsci.plugins.workflow.cps.CpsThread.runNextChunk(CpsThread.java:180)
	at PluginClassLoader for workflow-cps//org.jenkinsci.plugins.workflow.cps.CpsThreadGroup.run(CpsThreadGroup.java:422)
	at PluginClassLoader for workflow-cps//org.jenkinsci.plugins.workflow.cps.CpsThreadGroup$2.call(CpsThreadGroup.java:330)
	at PluginClassLoader for workflow-cps//org.jenkinsci.plugins.workflow.cps.CpsThreadGroup$2.call(CpsThreadGroup.java:294)
	at PluginClassLoader for workflow-cps//org.jenkinsci.plugins.workflow.cps.CpsVmExecutorService.lambda$wrap$4(CpsVmExecutorService.java:140)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at hudson.remoting.SingleLaneExecutorService$1.run(SingleLaneExecutorService.java:139)
	at jenkins.util.ContextResettingExecutorService$1.run(ContextResettingExecutorService.java:28)
	at jenkins.security.ImpersonatingExecutorService$1.run(ImpersonatingExecutorService.java:68)
	at jenkins.util.ErrorLoggingExecutorService.lambda$wrap$0(ErrorLoggingExecutorService.java:51)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at PluginClassLoader for workflow-cps//org.jenkinsci.plugins.workflow.cps.CpsVmExecutorService$1.call(CpsVmExecutorService.java:53)
	at PluginClassLoader for workflow-cps//org.jenkinsci.plugins.workflow.cps.CpsVmExecutorService$1.call(CpsVmExecutorService.java:50)
	at org.codehaus.groovy.runtime.GroovyCategorySupport$ThreadCategoryInfo.use(GroovyCategorySupport.java:136)
	at org.codehaus.groovy.runtime.GroovyCategorySupport.use(GroovyCategorySupport.java:275)
	at PluginClassLoader for workflow-cps//org.jenkinsci.plugins.workflow.cps.CpsVmExecutorService.lambda$categoryThreadFactory$0(CpsVmExecutorService.java:50)
	at java.base/java.lang.Thread.run(Thread.java:840)
Finished: FAILURE
